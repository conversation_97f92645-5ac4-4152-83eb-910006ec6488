from flask import Blueprint
from flask_login import login_required
from models.firmware import Firmware
from models.latest_firmware import LatestFirmware
from utils.logger import setup_logging
from flask import render_template, redirect, url_for, flash, request, send_file, jsonify
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import zlib
from models.database import db
from config import Config

# 获取日志记录器
logger = setup_logging()

# 固件管理相关路由
firmware_bp = Blueprint('firmware', __name__)

def allowed_file(filename, allowed_extensions={'bin'}):
    """检查文件类型是否允许上传"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

@firmware_bp.route('/firmware_list')
@login_required
def firmware_list():
    """固件列表页面"""
    firmwares = Firmware.query.all()
    return render_template('firmware_list.html', firmwares=firmwares)

@firmware_bp.route('/firmware/upload', methods=['POST'])
@login_required
def upload_firmware():
    """上传固件"""
    try:
        if 'firmware_file' not in request.files:
            if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                return jsonify({'success': False, 'message': '没有选择文件'})
            flash('没有选择文件', 'error')
            return redirect(url_for('firmware.firmware_list'))

        file = request.files['firmware_file']
        if file.filename == '':
            if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                return jsonify({'success': False, 'message': '没有选择文件'})
            flash('没有选择文件', 'error')
            return redirect(url_for('firmware.firmware_list'))

        if file and allowed_file(file.filename, {'bin'}):
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            base_name, ext = os.path.splitext(filename)

            # 检查文件是否已存在
            while os.path.exists(os.path.join(Config.FIRMWARE_UPLOAD_FOLDER, filename)):
                # 如果文件存在，添加时间戳
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{base_name}_{timestamp}{ext}"

            # 保存文件
            file_path = os.path.join(Config.FIRMWARE_UPLOAD_FOLDER, filename)
            file.save(file_path)

            # 计算CRC32和文件大小
            with open(file_path, 'rb') as f:
                file_data = f.read()
                crc32_value = format(zlib.crc32(file_data) & 0xFFFFFFFF, '08x')
                file_size = len(file_data)

            # 从固件文件中读取版本信息
            firmware_version = 'Unknown'
            try:
                with open(file_path, 'rb') as f:
                    firmware_data = f.read(32)
                    if len(firmware_data) >= 32:
                        # 从二进制数据中读取版本号
                        version_int = int.from_bytes(firmware_data[28:32], byteorder='little')
                        major = (version_int >> 16) & 0xFF
                        minor = (version_int >> 8) & 0xFF
                        patch = version_int & 0xFF
                        firmware_version = f"{major}.{minor}.{patch}"
            except Exception as e:
                logger.error(f"读取固件版本失败: {e}")

            # 获取设备类型
            device_type = request.form.get('device_type')
            if not device_type:
                error_msg = '请选择设备类型'
                if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                    return jsonify({'success': False, 'message': error_msg})
                flash(error_msg, 'error')
                return redirect(url_for('firmware.firmware_list'))

            try:
                device_type = int(device_type)
                if device_type not in [10, 50, 51]:
                    error_msg = '无效的设备类型'
                    if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                        return jsonify({'success': False, 'message': error_msg})
                    flash(error_msg, 'error')
                    return redirect(url_for('firmware.firmware_list'))
            except ValueError:
                error_msg = '设备类型格式错误'
                if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                    return jsonify({'success': False, 'message': error_msg})
                flash(error_msg, 'error')
                return redirect(url_for('firmware.firmware_list'))

            # 创建固件记录
            firmware = Firmware(
                name=request.form.get('firmware_name', filename),
                version=firmware_version,
                device_type=device_type,
                description=request.form.get('firmware_description', ''),
                file_path=file_path,
                size=file_size,
                crc32=crc32_value
            )

            db.session.add(firmware)
            db.session.commit()

            success_msg = '固件上传成功'
            if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                return jsonify({'success': True, 'message': success_msg})
            flash(success_msg, 'success')
        else:
            error_msg = '不支持的文件类型'
            if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
                return jsonify({'success': False, 'message': error_msg})
            flash(error_msg, 'error')

        return redirect(url_for('firmware.firmware_list'))

    except Exception as e:
        logger.error(f"上传固件失败: {e}")
        error_msg = f'上传固件失败: {str(e)}'
        if request.is_json or request.headers.get('Content-Type', '').startswith('multipart/form-data'):
            return jsonify({'success': False, 'message': error_msg})
        flash(error_msg, 'error')
        return redirect(url_for('firmware.firmware_list'))

@firmware_bp.route('/firmware/latest')
@login_required
def latest_firmware_management():
    """最新固件管理页面"""
    # 获取所有设备类型的最新固件设置
    latest_firmwares = LatestFirmware.query.all()

    # 获取所有固件按设备类型分组
    firmwares_by_type = {}
    for device_type in [10, 50, 51]:
        firmwares_by_type[device_type] = Firmware.query.filter_by(device_type=device_type).order_by(Firmware.upload_time.desc()).all()

    return render_template('latest_firmware.html',
                         latest_firmwares=latest_firmwares,
                         firmwares_by_type=firmwares_by_type)

@firmware_bp.route('/firmware/set_latest', methods=['POST'])
@login_required
def set_latest_firmware():
    """设置指定设备类型的最新固件"""
    try:
        device_type = request.form.get('device_type')
        firmware_id = request.form.get('firmware_id')

        if not device_type or not firmware_id:
            return jsonify({'success': False, 'message': '缺少必要参数'})

        device_type = int(device_type)
        firmware_id = int(firmware_id)

        # 验证设备类型
        if device_type not in [10, 50, 51]:
            return jsonify({'success': False, 'message': '无效的设备类型'})

        # 验证固件是否存在且类型匹配
        firmware = Firmware.query.get(firmware_id)
        if not firmware:
            return jsonify({'success': False, 'message': '固件不存在'})

        if firmware.device_type != device_type:
            return jsonify({'success': False, 'message': '固件类型与设备类型不匹配'})

        # 设置最新固件
        from flask_login import current_user
        LatestFirmware.set_latest_firmware_for_device_type(
            device_type,
            firmware_id,
            current_user.username if current_user else None
        )

        return jsonify({'success': True, 'message': '设置成功'})

    except Exception as e:
        logger.error(f"设置最新固件失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@firmware_bp.route('/firmware/download/<int:id>')
@login_required
def download_firmware(id):
    """下载固件"""
    firmware = Firmware.query.get_or_404(id)
    return send_file(
        firmware.file_path,
        as_attachment=True,
        download_name=f"{firmware.name}_v{firmware.version}.bin"
    )

@firmware_bp.route('/firmware/delete/<int:id>')
@login_required
def delete_firmware(id):
    """删除固件"""
    firmware = Firmware.query.get_or_404(id)
    
    # 删除文件
    if os.path.exists(firmware.file_path):
        os.remove(firmware.file_path)
    
    # 删除记录
    db.session.delete(firmware)
    db.session.commit()
    
    flash('固件删除成功', 'success')
    return redirect(url_for('firmware.firmware_list'))
