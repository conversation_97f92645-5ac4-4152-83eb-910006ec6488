import threading
import time
import uuid
from datetime import datetime
from typing import Optional, Any

from models.database import db
from models.device import Device
from models.firmware import Firmware
from models.latest_firmware import LatestFirmware
from models.batch_ota_report import BatchOtaReport, BatchOtaDetail
from services.iot_client_manager import IoTClientManager
from iot_client.functions.register_manager import RegisterManager
from iot_client.functions.ota_client import OtaClient
from utils.logger import LoggerManager

logger = LoggerManager.get_logger()


class BatchOtaService:
    """批量OTA更新服务 - 单线程顺序执行，避免系统过载"""

    def __init__(self):
        self.running_batches = {}  # 存储正在运行的批次
        self._lock = threading.Lock()

    def start_batch_ota(self, created_by: Optional[str] = None, skip_version_check: bool = False,
                       exclude_device_ids: Optional[str] = None, exclude_product_keys: Optional[str] = None,
                       exclude_keywords: Optional[str] = None) -> dict[str, Any]:
        """启动批量OTA更新

        Args:
            created_by: 创建者用户名
            skip_version_check: 是否跳过版本检查，强制更新所有设备
            exclude_device_ids: 要排除的设备ID列表（逗号分隔）
            exclude_product_keys: 要排除的产品密钥列表（逗号分隔）
            exclude_keywords: 要排除的关键字列表（逗号分隔，匹配设备ID或备注）

        Returns:
            Dict: 包含成功状态、批次ID和消息的字典
        """
        try:
            # 生成批次ID
            batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

            # 获取所有设备
            devices = Device.query.all()
            if not devices:
                return {"success": False, "message": "没有找到任何设备", "batch_id": None}

            # 应用设备过滤
            filtered_devices = self._filter_devices(devices, exclude_device_ids, exclude_product_keys, exclude_keywords)
            if not filtered_devices:
                return {"success": False, "message": "过滤后没有找到任何设备", "batch_id": None}

            # 创建批量报表
            report = BatchOtaReport(batch_id=batch_id, total_devices=len(filtered_devices), created_by=created_by)
            db.session.add(report)

            # 为每个设备创建详细记录
            for device in filtered_devices:
                detail = BatchOtaDetail(batch_id=batch_id, device_id=device.id, device_type=device.device_type)
                db.session.add(detail)

            db.session.commit()

            # 在后台线程中执行批量更新
            with self._lock:
                self.running_batches[batch_id] = True

            threading.Thread(target=self._execute_batch_ota, args=(batch_id, skip_version_check), daemon=True).start()

            logger.info(f"批量OTA更新已启动，批次ID: {batch_id}, 设备数量: {len(filtered_devices)}")

            return {"success": True, "message": f"批量OTA更新已启动，共{len(filtered_devices)}台设备",
                   "batch_id": batch_id}

        except Exception as e:
            logger.error(f"启动批量OTA更新失败: {e}")
            db.session.rollback()
            return {"success": False, "message": f"启动失败: {str(e)}", "batch_id": None}

    def _execute_batch_ota(self, batch_id: str, skip_version_check: bool = False):
        """执行批量OTA更新的主逻辑 - 单线程顺序执行"""
        try:
            # 创建应用上下文
            from app_factory import create_app

            app = create_app()

            with app.app_context():
                logger.info(f"开始执行批量OTA更新，批次ID: {batch_id}")

                # 获取所有待处理的设备ID（只获取ID，避免对象跨线程问题）
                detail_ids = db.session.query(BatchOtaDetail.id).filter_by(batch_id=batch_id).all()
                detail_ids = [detail_id[0] for detail_id in detail_ids]
                # 单线程顺序处理每个设备
                for i, detail_id in enumerate(detail_ids, 1):
                    try:
                        # 在每次循环中重新查询detail对象
                        detail = BatchOtaDetail.query.get(detail_id)
                        if not detail:
                            logger.error(f"设备详情记录不存在: detail_id={detail_id}")
                            continue

                        logger.info(f"处理设备 {i}/{len(detail_ids)}: {detail.device_name}")
                        self._process_single_device_sync(detail_id, batch_id, skip_version_check)

                    except Exception as e:
                        logger.error(f"处理设备时发生异常: {e}")
                        # 更新设备状态为失败，但继续处理下一个设备
                        try:
                            failed_detail = BatchOtaDetail.query.get(detail_id)
                            if failed_detail:
                                failed_detail.status = "失败"
                                failed_detail.error_message = f"处理异常: {str(e)}"
                                failed_detail.completed_at = datetime.now()
                                db.session.commit()
                        except Exception as update_error:
                            logger.error(f"更新设备状态失败: {update_error}")
                        continue

                # 更新报表状态
                report = BatchOtaReport.query.filter_by(batch_id=batch_id).first()
                if report:
                    report.update_counts()
                    logger.info(f"批量OTA更新完成，批次ID: {batch_id}")

        except Exception as e:
            logger.error(f"执行批量OTA更新异常: {e}")
        finally:
            # 清理运行状态
            with self._lock:
                self.running_batches.pop(batch_id, None)

    def _filter_devices(self, devices, exclude_device_ids, exclude_product_keys, exclude_keywords):
        """过滤设备列表"""
        filtered_devices = []

        # 解析排除列表
        excluded_device_ids = set()
        excluded_product_keys = set()
        excluded_keywords = set()

        if exclude_device_ids:
            excluded_device_ids = set(id.strip() for id in exclude_device_ids.split(',') if id.strip())
        if exclude_product_keys:
            excluded_product_keys = set(key.strip() for key in exclude_product_keys.split(',') if key.strip())
        if exclude_keywords:
            excluded_keywords = set(keyword.strip() for keyword in exclude_keywords.split(',') if keyword.strip())

        for device in devices:
            # 检查设备ID排除
            if device.device_id in excluded_device_ids:
                logger.info(f"设备 {device.device_id} 被设备ID排除")
                continue

            # 检查产品密钥排除
            if device.product_key in excluded_product_keys:
                logger.info(f"设备 {device.device_id} 被产品密钥排除")
                continue

            # 检查关键字排除
            excluded_by_keyword = False
            for keyword in excluded_keywords:
                if (keyword in device.device_id or
                    (device.device_remark and keyword in device.device_remark)):
                    logger.info(f"设备 {device.device_id} 被关键字 '{keyword}' 排除")
                    excluded_by_keyword = True
                    break

            if excluded_by_keyword:
                continue

            filtered_devices.append(device)

        logger.info(f"设备过滤完成: 原始{len(devices)}台 -> 过滤后{len(filtered_devices)}台")
        return filtered_devices

    def _process_single_device_sync(self, detail_id: int, batch_id: str, skip_version_check: bool = False):
        """同步处理单个设备的OTA更新"""
        try:
            # 重新查询detail和device信息
            detail = BatchOtaDetail.query.get(detail_id)
            if not detail:
                logger.error(f"设备详情记录不存在: detail_id={detail_id}")
                return

            device = Device.query.get(detail.device_id)
            if not device:
                logger.error(f"设备不存在: device_id={detail.device_id}")
                detail.status = "失败"
                detail.error_message = "设备不存在"
                detail.completed_at = datetime.now()
                db.session.commit()
                return

            if device.device_id not in {"9998", "9999"}:
                return

            detail.started_at = datetime.now()
            detail.status = "校验中"
            detail.stage = "查询设备固件信息"
            # 减少数据库commit频率，只在关键节点提交

            # 步骤1: 查询设备固件信息
            firmware_info = self._query_device_firmware_info(device, timeout=3)
            if not firmware_info:
                detail.status = "失败"
                detail.error_message = "设备离线或无响应"
                detail.completed_at = datetime.now()
                db.session.commit()
                return

            # 步骤2: 获取设备类型和当前固件版本
            device_type = firmware_info.get("device_type", 0)
            current_app_version = firmware_info.get("app_version", "0.0.0")

            # 更新设备类型到数据库
            if device_type in [10, 50, 51] and device.device_type != device_type:
                device.device_type = device_type

            detail.device_type = device_type
            detail.current_firmware_crc = current_app_version  # 复用字段存储版本号
            detail.stage = "查找目标固件"

            # 步骤3: 查找目标固件
            target_firmware = LatestFirmware.get_latest_firmware_for_device_type(device_type)
            if not target_firmware:
                detail.status = "失败"
                detail.error_message = f"未找到设备类型{device_type}的最新固件"
                detail.completed_at = datetime.now()
                db.session.commit()
                return

            detail.target_firmware_id = target_firmware.id
            detail.target_firmware_crc = target_firmware.version  # 复用字段存储目标版本号
            detail.stage = "校验固件版本"
            # 合并数据库操作，减少commit次数

            # 步骤4: 校验是否需要更新（比较版本号）
            if not skip_version_check and current_app_version == target_firmware.version:
                detail.status = "跳过"
                detail.error_message = "固件版本已是最新，无需更新"
                detail.completed_at = datetime.now()
                db.session.commit()
                logger.info(
                    f"设备 {device.device_id} 固件版本({current_app_version})已是最新版本({target_firmware.version})，跳过更新"
                )
                return
            elif skip_version_check:
                logger.info(f"设备 {device.device_id} 跳过版本检查，强制更新")
            else:
                logger.info(f"设备 {device.device_id} 需要更新: {current_app_version} -> {target_firmware.version}")

            # 步骤5: 执行OTA更新
            detail.status = "更新中"
            detail.stage = "执行OTA更新"
            db.session.commit()

            success = self._perform_ota_update(device, target_firmware)

            # 更新最终状态
            detail.status = "成功" if success else "失败"
            detail.error_message = "" if success else "OTA更新失败"
            detail.completed_at = datetime.now()

            if success:
                device.firmware_version = target_firmware.version
                device.last_ota_time = datetime.now()
                device.last_ota_status = "成功"
            else:
                device.last_ota_status = "失败"

            db.session.commit()

            logger.info(f"设备 {device.device_id} OTA更新{'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"处理设备时发生异常: {e}")
            try:
                detail = BatchOtaDetail.query.get(detail_id)
                if detail:
                    detail.status = "失败"
                    detail.error_message = f"处理异常: {str(e)}"
                    detail.completed_at = datetime.now()
                    db.session.commit()
            except Exception as commit_error:
                logger.error(f"更新设备状态时发生异常: {commit_error}")
        finally:
            # 确保清理数据库会话
            try:
                db.session.remove()
            except Exception:
                pass

    def _query_device_firmware_info(self, device: Device, timeout: int = 10) -> Optional[dict]:
        """查询设备固件信息或版本寄存器"""
        # 代码选项：True=使用固件信息查询，False=使用寄存器读取
        USE_FIRMWARE_INFO_QUERY = False

        if USE_FIRMWARE_INFO_QUERY:
            return self._query_firmware_info_command(device, timeout)
        else:
            return self._query_version_registers(device, timeout)

    def _query_firmware_info_command(self, device: Device, timeout: int = 10) -> Optional[dict]:
        """通过固件信息查询命令获取设备信息"""
        try:
            iot_client = IoTClientManager.get_instance()
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            msg = reg_manager.cmd_request_firmware_info_query(int(device.device_id), timeout)
            if msg and msg.get("parsed_data", {}).get("result") == 0:
                return msg["parsed_data"].get("info", {})

            return None

        except Exception as e:
            logger.error(f"查询设备 {device.device_id} 固件信息失败: {e}")
            return None

    def _query_version_registers(self, device: Device, timeout: int = 10) -> Optional[dict]:
        """通过版本寄存器获取设备类型和版本号"""
        try:
            iot_client = IoTClientManager.get_instance()
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 读取REG_VERSION_H (22) 和 REG_VERSION_L (23)
            version_h_msg = reg_manager.read_registers(int(device.device_id), 22, 1, timeout)  # REG_VERSION_H
            logger.info(f"设备 {device.device_id} REG_VERSION_H 返回数据: {version_h_msg}")

            # 检查是否是超时错误回复
            if not version_h_msg:
                logger.error(f"读取设备 {device.device_id} REG_VERSION_H失败: 无返回数据")
                return None

            # 检查是否是错误回复（有result字段且为error）
            if version_h_msg.get('result') == 'error':
                error_msg = version_h_msg.get('error', '未知错误')
                logger.error(f"读取设备 {device.device_id} REG_VERSION_H失败: {error_msg}")
                return None

            # 检查是否有parsed_data字段
            if 'parsed_data' not in version_h_msg:
                logger.error(f"读取设备 {device.device_id} REG_VERSION_H失败: 缺少parsed_data字段")
                return None

            version_l_msg = reg_manager.read_registers(int(device.device_id), 23, 1, timeout)  # REG_VERSION_L
            logger.info(f"设备 {device.device_id} REG_VERSION_L 返回数据: {version_l_msg}")

            # 检查是否是超时错误回复
            if not version_l_msg:
                logger.error(f"读取设备 {device.device_id} REG_VERSION_L失败: 无返回数据")
                return None

            # 检查是否是错误回复（有result字段且为error）
            if version_l_msg.get('result') == 'error':
                error_msg = version_l_msg.get('error', '未知错误')
                logger.error(f"读取设备 {device.device_id} REG_VERSION_L失败: {error_msg}")
                return None

            # 检查是否有parsed_data字段
            if 'parsed_data' not in version_l_msg:
                logger.error(f"读取设备 {device.device_id} REG_VERSION_L失败: 缺少parsed_data字段")
                return None

            # 从parsed_data中提取寄存器值
            # 正确的数据结构: {'parsed_data': {'register_value': [value]}}
            version_h_parsed = version_h_msg.get('parsed_data', {})
            version_l_parsed = version_l_msg.get('parsed_data', {})

            version_h_values = version_h_parsed.get('register_value', [])
            version_l_values = version_l_parsed.get('register_value', [])

            if not version_h_values or not version_l_values:
                logger.error(f"设备 {device.device_id} 寄存器值为空")
                return None

            version_h = version_h_values[0] if isinstance(version_h_values, list) else version_h_values
            version_l = version_l_values[0] if isinstance(version_l_values, list) else version_l_values

            # 解析设备类型：REG_VERSION_H的高8位
            device_type = (version_h >> 8) & 0xFF

            # 检查设备类型是否有效（不能为0）
            if device_type == 0:
                logger.error(f"设备 {device.device_id} 设备类型为0，可能是读取失败或设备未正确配置")
                return None

            # 使用统一的版本解析工具
            from utils.ota_common import VersionUtils
            app_version = VersionUtils.parse_version_from_registers(version_h, version_l)

            logger.info(f"设备 {device.device_id} 版本信息: 类型={device_type}, APP版本={app_version}")

            return {
                "device_type": device_type,
                "app_version": app_version,
                "version_h": version_h,
                "version_l": version_l,
            }

        except Exception as e:
            logger.error(f"查询设备 {device.device_id} 版本寄存器异常: {e}")
            return None

    def _perform_ota_update(self, device: Device, firmware: Firmware) -> bool:
        """执行OTA更新"""
        try:
            iot_client = IoTClientManager.get_instance()
            ota_client = OtaClient(
                iot_client,
                device.product_key,
                device.device_id,
                logger,
                lambda *args: None,  # 接受任意数量参数的进度回调
            )

            return ota_client.start_ota(firmware.file_path, force_update=True)

        except Exception as e:
            logger.error(f"设备 {device.device_id} OTA更新异常: {e}")
            return False

    def get_batch_status(self, batch_id: str) -> Optional[dict]:
        """获取批次状态"""
        report = BatchOtaReport.query.filter_by(batch_id=batch_id).first()
        if not report:
            return None

        return {
            "batch_id": batch_id,
            "status": report.status,
            "total_devices": report.total_devices,
            "success_count": report.success_count,
            "failed_count": report.failed_count,
            "skipped_count": report.skipped_count,
            "progress_percentage": report.progress_percentage,
            "started_at": report.started_at.strftime("%Y-%m-%d %H:%M:%S"),
            "completed_at": report.completed_at.strftime("%Y-%m-%d %H:%M:%S") if report.completed_at else None,
        }

    def is_batch_running(self, batch_id: str) -> bool:
        """检查批次是否正在运行"""
        with self._lock:
            return batch_id in self.running_batches


# 全局批量OTA服务实例
batch_ota_service = BatchOtaService()
