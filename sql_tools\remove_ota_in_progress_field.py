#!/usr/bin/env python3
"""
移除设备表中的 ota_in_progress 字段

这个脚本用于移除 Device 模型中不再需要的 ota_in_progress 字段。
由于随时停机的需求，这个字段变得不可靠，因此移除它。
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app, db
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def remove_ota_in_progress_field():
    """移除 ota_in_progress 字段"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查字段是否存在
            result = db.session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'device'
                AND column_name = 'ota_in_progress'
            """))
            
            if result.fetchone():
                logger.info("发现 ota_in_progress 字段，开始移除...")
                
                # 移除字段
                db.session.execute(text("ALTER TABLE device DROP COLUMN ota_in_progress"))
                db.session.commit()
                
                logger.info("✅ 成功移除 ota_in_progress 字段")
            else:
                logger.info("ota_in_progress 字段不存在，无需移除")
                
        except Exception as e:
            logger.error(f"❌ 移除字段失败: {e}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    remove_ota_in_progress_field()
    print("数据库字段移除完成")
