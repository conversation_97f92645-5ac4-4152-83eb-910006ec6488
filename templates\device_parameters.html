{% extends "base.html" %}

{% block title %}设备参数 - {{ device.device_id }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-microchip text-primary me-2" style="font-size: 1.5rem;"></i>
                            <h5 class="mb-0">设备参数 - {{ device.device_id }}</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge rounded-pill bg-primary-subtle text-primary me-2">
                                <i class="fas fa-info-circle me-1"></i> 设备参数: <span id="param-count">9999</span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body pb-0">
                    <!-- 操作按钮区域 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-primary" onclick="queryParameters()">
                                    <i class="fas fa-sync-alt me-1"></i> 查询参数
                                </button>
                                <button class="btn btn-secondary" onclick="queryDebugInfo()">
                                    <i class="fas fa-bug me-1"></i> 查询调试信息
                                </button>
                                <button class="btn btn-info" onclick="queryFirmwareInfo()">
                                    <i class="fas fa-microchip me-1"></i> 查询固件信息
                                </button>
                                <button class="btn btn-warning" onclick="queryErrorCounts()">
                                    <i class="fas fa-exclamation-triangle me-1"></i> 查询错误计数
                                </button>
                                <button class="btn btn-info" onclick="queryDeviceLocation()">
                                    <i class="fas fa-map-marker-alt me-1"></i> 查询位置
                                </button>
                                <button class="btn btn-success" onclick="openDebugScriptModal()">
                                    <i class="fas fa-code me-1"></i> 调试脚本
                                </button>
                                <button class="btn btn-success" onclick="querySimInfo()">
                                    <i class="fas fa-sim-card me-1"></i> 查询SIM卡信息
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                                <button class="btn btn-success me-2" id="exportParametersBtn">
                                    <i class="fas fa-download me-1"></i> 导出参数
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="historyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-chart-line me-1"></i> 历史数据
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="historyDropdown">
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.power_history_page', device_id=device.id) }}">
                                                <i class="fas fa-bolt me-2 text-warning"></i> 功率
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.temperature_history_page', device_id=device.id) }}">
                                                <i class="fas fa-thermometer-half me-2 text-danger"></i> 温度
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.voltage_history_page', device_id=device.id) }}">
                                                <i class="fas fa-plug me-2 text-info"></i> 电压
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.csq_history_page', device_id=device.id) }}">
                                                <i class="fas fa-signal me-2 text-success"></i> 信号质量
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 参数分类标签 -->
                    <div class="mb-3">
                        <ul class="nav nav-pills" id="parameterTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                                    <i class="fas fa-list me-1"></i> 全部
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="time-tab" data-bs-toggle="pill" data-bs-target="#time" type="button" role="tab" aria-controls="time" aria-selected="false">
                                    <i class="fas fa-clock me-1"></i> 时间参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="power-tab" data-bs-toggle="pill" data-bs-target="#power" type="button" role="tab" aria-controls="power" aria-selected="false">
                                    <i class="fas fa-bolt me-1"></i> 功率参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="other-tab" data-bs-toggle="pill" data-bs-target="#other" type="button" role="tab" aria-controls="other" aria-selected="false">
                                    <i class="fas fa-sliders-h me-1"></i> 其他参数
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- 参数表格 -->
                    <div class="tab-content" id="parameterTabsContent">
                        <!-- 全部参数 -->
                        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                <tr>
                                    <td>REG_T1</td>
                                    <td>0</td>
                                    <td id="REG_T1">--</td>
                                    <td>长时间未插入充电器检测时间(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T1', 0)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T2</td>
                                    <td>1</td>
                                    <td id="REG_T2">--</td>
                                    <td>功率大于0连续时间，判定为已连接充电器(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T2', 1)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T3</td>
                                    <td>2</td>
                                    <td id="REG_T3">--</td>
                                    <td>浮充时间，大于该时间判定为电量已满(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T3', 2)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T4</td>
                                    <td>3</td>
                                    <td id="REG_T4">--</td>
                                    <td>功率超过限制判定时间(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T4', 3)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T5</td>
                                    <td>4</td>
                                    <td id="REG_T5">--</td>
                                    <td>总功率超过限制触发时间(单位：ms)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T5', 4)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T6</td>
                                    <td>5</td>
                                    <td id="REG_T6">--</td>
                                    <td>温度超过阈值判定时间(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T6', 5)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T7</td>
                                    <td>6</td>
                                    <td id="REG_T7">--</td>
                                    <td>初始单个口功率过大判定时间(单位：ms)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T7', 6)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T8</td>
                                    <td>7</td>
                                    <td id="REG_T8">--</td>
                                    <td>充电过程中继电器开路状态判断为中控断电的时间(单位：ms)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T8', 7)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T9</td>
                                    <td>8</td>
                                    <td id="REG_T9">--</td>
                                    <td>首次进入充电过程中功率突降为0时的浮充时间(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T9', 8)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T10</td>
                                    <td>9</td>
                                    <td id="REG_T10">--</td>
                                    <td>无线充电浮充时间(单位：s)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T10', 9)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P1</td>
                                    <td>10</td>
                                    <td id="REG_P1">--</td>
                                    <td>浮充功率阈值(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P1', 10)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2</td>
                                    <td>11</td>
                                    <td id="REG_P2">--</td>
                                    <td>单口充电过程中的功率限制(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2', 11)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3</td>
                                    <td>12</td>
                                    <td id="REG_P3">--</td>
                                    <td>单口充电过程中的安全功率限制(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3', 12)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P4</td>
                                    <td>13</td>
                                    <td id="REG_P4">--</td>
                                    <td>总功率限制(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P4', 13)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5</td>
                                    <td>14</td>
                                    <td id="REG_P5">--</td>
                                    <td>单口初始安全功率限制(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5', 14)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P6</td>
                                    <td>15</td>
                                    <td id="REG_P6">--</td>
                                    <td>启动充电后检测充电负载存在阈值(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P6', 15)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P7</td>
                                    <td>16</td>
                                    <td id="REG_P7">--</td>
                                    <td>无线充电浮充功率阈值(单位：W)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P7', 16)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P8</td>
                                    <td>17</td>
                                    <td id="REG_P8">--</td>
                                    <td>判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接(单位：V5板子为BL0910的有功功率的寄存器值，V2板子为mW)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P8', 17)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_T11</td>
                                    <td>18</td>
                                    <td id="REG_T11">--</td>
                                    <td>拔出充电器的判定时间(单位：秒)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_T11', 18)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_CTRL1</td>
                                    <td>19</td>
                                    <td id="REG_CTRL1">--</td>
                                    <td>控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式。</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_CTRL1', 19)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_TEMP1</td>
                                    <td>20</td>
                                    <td id="REG_TEMP1">--</td>
                                    <td>过温保护阈值(单位：℃)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_TEMP1', 20)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_BOOT_CNT</td>
                                    <td>21</td>
                                    <td id="REG_BOOT_CNT">--</td>
                                    <td>启动计数</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_BOOT_CNT', 21)" disabled>
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_VERSION_H</td>
                                    <td>22</td>
                                    <td id="REG_VERSION_H">--</td>
                                    <td>版本号高字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_VERSION_H', 22)" disabled>
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_VERSION_L</td>
                                    <td>23</td>
                                    <td id="REG_VERSION_L">--</td>
                                    <td>版本号低字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_VERSION_L', 23)" disabled>
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_PERSENTAGE</td>
                                    <td>24</td>
                                    <td id="REG_PERSENTAGE">--</td>
                                    <td>拔出插头判定百分比(单位：%)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_PERSENTAGE', 24)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_CSQ</td>
                                    <td>25</td>
                                    <td id="REG_CSQ">--</td>
                                    <td>信号强度(CSQ)和误码率(BER)</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_CSQ', 25)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_LOCATION_CODE</td>
                                    <td>26</td>
                                    <td id="REG_LOCATION_CODE">--</td>
                                    <td>位置编码</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_LOCATION_CODE', 26)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_LOCATION_LATITUDE_H</td>
                                    <td>27</td>
                                    <td id="REG_LOCATION_LATITUDE_H">--</td>
                                    <td>纬度高字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_LOCATION_LATITUDE_H', 27)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_LOCATION_LATITUDE_L</td>
                                    <td>28</td>
                                    <td id="REG_LOCATION_LATITUDE_L">--</td>
                                    <td>纬度低字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_LOCATION_LATITUDE_L', 28)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_LOCATION_LONGITUDE_H</td>
                                    <td>29</td>
                                    <td id="REG_LOCATION_LONGITUDE_H">--</td>
                                    <td>经度高字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_LOCATION_LONGITUDE_H', 29)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_LOCATION_LONGITUDE_L</td>
                                    <td>30</td>
                                    <td id="REG_LOCATION_LONGITUDE_L">--</td>
                                    <td>经度低字节</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_LOCATION_LONGITUDE_L', 30)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_ERROR_CNT1</td>
                                    <td>31</td>
                                    <td id="REG_ERROR_CNT1">--</td>
                                    <td>临时错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_ERROR_CNT1', 31)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_ERROR_CNT2</td>
                                    <td>32</td>
                                    <td id="REG_ERROR_CNT2">--</td>
                                    <td>临时错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_ERROR_CNT2', 32)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_UID_PROTECT_KEY1</td>
                                    <td>33</td>
                                    <td id="REG_UID_PROTECT_KEY1">--</td>
                                    <td>UID保护密钥1：key1</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_UID_PROTECT_KEY1', 33)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_HEART_AND_BILLING_PROTO_TYPE</td>
                                    <td>34</td>
                                    <td id="REG_HEART_AND_BILLING_PROTO_TYPE">--</td>
                                    <td>MQTT服务器的类型</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_HEART_AND_BILLING_PROTO_TYPE', 34)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_RESERV3</td>
                                    <td>35</td>
                                    <td id="REG_RESERV3">--</td>
                                    <td>保留3</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_RESERV3', 35)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_RESERV4</td>
                                    <td>36</td>
                                    <td id="REG_RESERV4">--</td>
                                    <td>保留4</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_RESERV4', 36)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_FACTORY_FAULT</td>
                                    <td>37</td>
                                    <td id="REG_FACTORY_FAULT">--</td>
                                    <td>工厂故障记录1</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_FACTORY_FAULT', 37)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_FACTORY_FAULT2</td>
                                    <td>38</td>
                                    <td id="REG_FACTORY_FAULT2">--</td>
                                    <td>工厂故障记录2</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_FACTORY_FAULT2', 38)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 插座功率阈值参数 -->
                                <tr class="table-info">
                                    <td colspan="5"><strong>插座功率阈值配置</strong></td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG0</td>
                                    <td>39</td>
                                    <td id="REG_P2_PLUG0">--</td>
                                    <td>插座0的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG0', 39)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG0</td>
                                    <td>40</td>
                                    <td id="REG_P3_PLUG0">--</td>
                                    <td>插座0的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG0', 40)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG0</td>
                                    <td>41</td>
                                    <td id="REG_P5_PLUG0">--</td>
                                    <td>插座0的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG0', 41)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG1</td>
                                    <td>42</td>
                                    <td id="REG_P2_PLUG1">--</td>
                                    <td>插座1的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG1', 42)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG1</td>
                                    <td>43</td>
                                    <td id="REG_P3_PLUG1">--</td>
                                    <td>插座1的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG1', 43)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG1</td>
                                    <td>44</td>
                                    <td id="REG_P5_PLUG1">--</td>
                                    <td>插座1的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG1', 44)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG2</td>
                                    <td>45</td>
                                    <td id="REG_P2_PLUG2">--</td>
                                    <td>插座2的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG2', 45)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG2</td>
                                    <td>46</td>
                                    <td id="REG_P3_PLUG2">--</td>
                                    <td>插座2的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG2', 46)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG2</td>
                                    <td>47</td>
                                    <td id="REG_P5_PLUG2">--</td>
                                    <td>插座2的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG2', 47)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG3</td>
                                    <td>48</td>
                                    <td id="REG_P2_PLUG3">--</td>
                                    <td>插座3的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG3', 48)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG3</td>
                                    <td>49</td>
                                    <td id="REG_P3_PLUG3">--</td>
                                    <td>插座3的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG3', 49)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG3</td>
                                    <td>50</td>
                                    <td id="REG_P5_PLUG3">--</td>
                                    <td>插座3的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG3', 50)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG4</td>
                                    <td>51</td>
                                    <td id="REG_P2_PLUG4">--</td>
                                    <td>插座4的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG4', 51)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG4</td>
                                    <td>52</td>
                                    <td id="REG_P3_PLUG4">--</td>
                                    <td>插座4的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG4', 52)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG4</td>
                                    <td>53</td>
                                    <td id="REG_P5_PLUG4">--</td>
                                    <td>插座4的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG4', 53)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG5</td>
                                    <td>54</td>
                                    <td id="REG_P2_PLUG5">--</td>
                                    <td>插座5的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG5', 54)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG5</td>
                                    <td>55</td>
                                    <td id="REG_P3_PLUG5">--</td>
                                    <td>插座5的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG5', 55)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG5</td>
                                    <td>56</td>
                                    <td id="REG_P5_PLUG5">--</td>
                                    <td>插座5的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG5', 56)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG6</td>
                                    <td>57</td>
                                    <td id="REG_P2_PLUG6">--</td>
                                    <td>插座6的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG6', 57)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG6</td>
                                    <td>58</td>
                                    <td id="REG_P3_PLUG6">--</td>
                                    <td>插座6的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG6', 58)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG6</td>
                                    <td>59</td>
                                    <td id="REG_P5_PLUG6">--</td>
                                    <td>插座6的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG6', 59)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG7</td>
                                    <td>60</td>
                                    <td id="REG_P2_PLUG7">--</td>
                                    <td>插座7的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG7', 60)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG7</td>
                                    <td>61</td>
                                    <td id="REG_P3_PLUG7">--</td>
                                    <td>插座7的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG7', 61)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG7</td>
                                    <td>62</td>
                                    <td id="REG_P5_PLUG7">--</td>
                                    <td>插座7的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG7', 62)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG8</td>
                                    <td>63</td>
                                    <td id="REG_P2_PLUG8">--</td>
                                    <td>插座8的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG8', 63)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG8</td>
                                    <td>64</td>
                                    <td id="REG_P3_PLUG8">--</td>
                                    <td>插座8的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG8', 64)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG8</td>
                                    <td>65</td>
                                    <td id="REG_P5_PLUG8">--</td>
                                    <td>插座8的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG8', 65)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P2_PLUG9</td>
                                    <td>66</td>
                                    <td id="REG_P2_PLUG9">--</td>
                                    <td>插座9的P2功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P2_PLUG9', 66)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P3_PLUG9</td>
                                    <td>67</td>
                                    <td id="REG_P3_PLUG9">--</td>
                                    <td>插座9的P3功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P3_PLUG9', 67)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_P5_PLUG9</td>
                                    <td>68</td>
                                    <td id="REG_P5_PLUG9">--</td>
                                    <td>插座9的P5功率阈值</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_P5_PLUG9', 68)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>REG_UID_PROTECT_KEY2</td>
                                    <td>77</td>
                                    <td id="REG_UID_PROTECT_KEY2">--</td>
                                    <td>UID保护密钥2：key2</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editParameter('REG_UID_PROTECT_KEY2', 77)">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 时间参数 -->
                        <div class="tab-pane fade" id="time" role="tabpanel" aria-labelledby="time-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="time-params">
                                        <!-- 时间参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 功率参数 -->
                        <div class="tab-pane fade" id="power" role="tabpanel" aria-labelledby="power-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="power-params">
                                        <!-- 功率参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 其他参数 -->
                        <div class="tab-pane fade" id="other" role="tabpanel" aria-labelledby="other-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="other-params">
                                        <!-- 其他参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑参数模态框 -->
<div class="modal fade" id="editParameterModal" tabindex="-1" aria-labelledby="editParameterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editParameterModalLabel">编辑参数</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editParameterForm">
                    <input type="hidden" id="paramName">
                    <input type="hidden" id="paramAddr">
                    <div class="mb-3">
                        <label class="form-label fw-bold" id="paramNameLabel">参数名称</label>
                        <p class="text-muted mb-3" id="paramDescription">参数描述</p>
                        <label for="paramValue" class="form-label">参数值</label>
                        <input type="number" class="form-control" id="paramValue" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveParameter()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 设备调试信息模态框 -->
<div class="modal fade" id="debugInfoModal" tabindex="-1" aria-labelledby="debugInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="debugInfoModalLabel">设备调试信息 - {{ device.device_id }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="debugInfoLoading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备调试信息，请稍候...</p>
                </div>
                <div id="debugInfoContent" class="d-none">
                    <!-- 调试信息内容将通过JavaScript动态填充 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">错误计数</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>BL0910错误计数:</th>
                                                <td id="bl0910_error_count">--</td>
                                            </tr>
                                            <tr>
                                                <th>短周期错误计数:</th>
                                                <td id="short_period_error_count">--</td>
                                            </tr>
                                            <tr>
                                                <th>长周期错误计数:</th>
                                                <td id="long_period_error_count">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">继电器状态</h6>
                                </div>
                                <div class="card-body">
                                    <div id="relay_state_value" class="mb-2">继电器状态值: --</div>
                                    <div class="row" id="relay_bits_container">
                                        <!-- 继电器状态位将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">BL0910 RMS寄存器值</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>通道</th>
                                                    <th>寄存器值</th>
                                                    <th>计算功率 (W)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bl0910_rms_regs_container">
                                                <!-- BL0910 RMS寄存器值将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">其他信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>零交叉时间:</th>
                                                <td id="zero_cross_time">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">传感器数据</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>电压有效值:</th>
                                                <td id="voltage_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>温度:</th>
                                                <td id="temperature_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>总有功功率:</th>
                                                <td id="total_power_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>信号质量 (CSQ):</th>
                                                <td id="csq_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>误码率 (BER):</th>
                                                <td id="ber_value">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div id="debugInfoError" class="alert alert-danger d-none">
                    获取设备调试信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryDebugInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 固件信息查询模态框 -->
<div class="modal fade" id="firmwareInfoModal" tabindex="-1" aria-labelledby="firmwareInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="firmwareInfoModalLabel">设备固件信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="firmwareInfoLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备固件信息...</p>
                </div>
                <div id="firmwareInfoContent" class="d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">固件1 (FW1) 信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>固件大小:</th>
                                                <td id="fw1_size">--</td>
                                            </tr>
                                            <tr>
                                                <th>CRC32校验:</th>
                                                <td id="fw1_crc32">--</td>
                                            </tr>
                                            <tr>
                                                <th>更新时间:</th>
                                                <td id="fw1_update_time">--</td>
                                            </tr>
                                            <tr>
                                                <th>编译时间:</th>
                                                <td id="compile_ts">--</td>
                                            </tr>
                                            <tr>
                                                <th>分区容量:</th>
                                                <td id="fw1_capacity">--</td>
                                            </tr>
                                            <tr>
                                                <th>固件版本:</th>
                                                <td id="fw1_version">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">固件2 (FW2) 信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>固件大小:</th>
                                                <td id="fw2_size">--</td>
                                            </tr>
                                            <tr>
                                                <th>CRC32校验:</th>
                                                <td id="fw2_crc32">--</td>
                                            </tr>
                                            <tr>
                                                <th>更新时间:</th>
                                                <td id="fw2_update_time">--</td>
                                            </tr>
                                            <tr>
                                                <th>分区容量:</th>
                                                <td id="fw2_capacity">--</td>
                                            </tr>
                                            <tr>
                                                <th>固件版本:</th>
                                                <td id="fw2_version">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">系统信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>Bootloader版本:</th>
                                                <td id="bootloader_version">--</td>
                                            </tr>
                                            <tr>
                                                <th>设备类型:</th>
                                                <td id="device_type">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">设备唯一标识 (UID)</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>UID[0]:</th>
                                                <td id="uid_0">--</td>
                                            </tr>
                                            <tr>
                                                <th>UID[1]:</th>
                                                <td id="uid_1">--</td>
                                            </tr>
                                            <tr>
                                                <th>UID[2]:</th>
                                                <td id="uid_2">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="firmwareInfoError" class="alert alert-danger d-none">
                    获取设备固件信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryFirmwareInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 引入调试脚本模态框 -->
{% include 'components/debug_script_modal.html' %}
{% endblock %}

{% block scripts %}
<script>
    // BL0910 功率计算系数 [斜率, 截距]
    const BL0910_ACTIVE_POWER_COEF = [
        [-0.0014, -0.8803],  /* MEASURE_1 */
        [-0.0014, -0.6832],  /* MEASURE_2 */
        [0.0014, -0.726],    /* MEASURE_3 */
        [0.0014, -0.6832],   /* MEASURE_4 */
        [0.0014, -0.6832],   /* MEASURE_5 */
        [0.00134339, -0.8044], /* MEASURE_6 */
        [0.00134339, -0.8044], /* MEASURE_7 */
        [-0.0014, -0.6832],  /* MEASURE_8 */
        [-0.0014, -0.6832],  /* MEASURE_9 */
        [0.0014, -0.8305]    /* MEASURE_10 */
    ];

    // 计算BL0910寄存器值对应的功率
    function calculatePowerFromRegister(registerValue, channelIndex) {
        // 将寄存器值视为int32_t整数
        // JavaScript中没有int32_t类型，但可以通过位运算模拟
        const int32Value = registerValue | 0; // 强制转换为32位有符号整数

        // 获取对应通道的系数
        const coef = BL0910_ACTIVE_POWER_COEF[channelIndex];

        // 应用线性变换: power = slope * register_value + intercept
        const power = coef[0] * int32Value + coef[1];

        // 返回计算结果，保留2位小数
        return power.toFixed(2);
    }

    // 参数分类
    const timeParams = ['REG_T1', 'REG_T2', 'REG_T3', 'REG_T4', 'REG_T5', 'REG_T6', 'REG_T7', 'REG_T8', 'REG_T9', 'REG_T10', 'REG_T11'];
    const powerParams = ['REG_P1', 'REG_P2', 'REG_P3', 'REG_P4', 'REG_P5', 'REG_P6', 'REG_P7', 'REG_P8'];

    // 页面加载时自动获取参数
    document.addEventListener('DOMContentLoaded', function() {
        // 首先尝试从数据库获取保存的参数
        fetchSavedParameters();

        // 初始化参数分类
        initializeParameterCategories();

        // 更新参数计数
        updateParameterCount();

        // 添加标签切换事件监听
        document.querySelectorAll('#parameterTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(event) {
                // 当标签被激活时，确保对应的参数表格是最新的
                updateParameterCategories();
            });
        });
    });

    // 初始化参数分类
    function initializeParameterCategories() {
        // 获取所有参数行
        const allRows = document.querySelectorAll('#all tbody tr');

        // 清空分类表格
        document.querySelector('.time-params').innerHTML = '';
        document.querySelector('.power-params').innerHTML = '';
        document.querySelector('.other-params').innerHTML = '';

        // 分类并复制参数行到对应的表格
        allRows.forEach(row => {
            const paramName = row.querySelector('td:first-child').textContent;
            const clonedRow = row.cloneNode(true);

            if (timeParams.includes(paramName)) {
                document.querySelector('.time-params').appendChild(clonedRow);
            } else if (powerParams.includes(paramName)) {
                document.querySelector('.power-params').appendChild(clonedRow);
            } else {
                document.querySelector('.other-params').appendChild(clonedRow);
            }
        });
    }

    // 更新参数分类
    function updateParameterCategories() {
        initializeParameterCategories();
    }

    // 更新参数计数
    function updateParameterCount() {
        const allParams = document.querySelectorAll('#all tbody tr').length;
        document.getElementById('param-count').textContent = allParams;
    }

    // 从数据库获取保存的参数
    function fetchSavedParameters() {
        // 显示加载中
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '--';
        });

        // 发送请求获取保存的参数
        fetch('/api/device/{{ device.id }}/saved_parameters')
            .then(response => response.json())
            .then(data => {
                // 更新参数值
                for (const [key, paramData] of Object.entries(data)) {
                    const elements = document.querySelectorAll(`[id="${key}"]`);
                    elements.forEach(element => {
                        if (element) {
                            if (typeof paramData === 'object' && paramData !== null && 'value' in paramData) {
                                element.textContent = paramData.value;
                            } else {
                                element.textContent = paramData;
                            }
                        }
                    });
                }

                // 更新参数分类
                updateParameterCategories();
            })
            .catch(error => {
                console.error('获取保存的参数失败:', error);
                // 如果获取保存的参数失败，恢复默认显示
                paramElements.forEach(el => {
                    el.textContent = '--';
                });
            });
    }

    // 查询设备位置
    function queryDeviceLocation() {
        // 显示加载中提示
        const locationBtn = document.querySelector('button[onclick="queryDeviceLocation()"]');
        const originalText = locationBtn.innerHTML;
        locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        locationBtn.disabled = true;

        // 发送请求获取设备位置
        fetch('/api/device/{{ device.id }}/location')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('获取设备位置失败: ' + data.error);
                } else if (data.success && data.location) {
                    const location = data.location;
                    alert(`设备位置查询成功:\n位置代码: ${location.location_code}\n纬度: ${location.latitude}\n经度: ${location.longitude}`);
                } else {
                    alert('设备位置查询成功，已更新到数据库');
                }
            })
            .catch(error => {
                console.error('获取设备位置失败:', error);
                alert('获取设备位置失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                locationBtn.innerHTML = originalText;
                locationBtn.disabled = false;
            });
    }

    // 查询参数
    function queryParameters() {
        // 显示加载中
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '加载中...';
        });

        // 发送请求获取参数
        fetch('/api/device/{{ device.id }}/parameters')
            .then(response => response.json())
            .then(data => {
                // 遍历返回的数据
                Object.entries(data).forEach(([paramName, paramData]) => {
                    // 查找对应的元素
                    const element = document.getElementById(paramName);
                    if (element && paramData && typeof paramData === 'object' && 'value' in paramData) {
                        element.textContent = paramData.value;
                    }
                });

                // 更新参数分类
                updateParameterCategories();
            })
            .catch(error => {
                console.error('获取参数失败:', error);
                alert('获取参数失败，请重试');
                // 恢复默认显示
                paramElements.forEach(el => {
                    el.textContent = '--';
                });
            });
    }

    // 编辑参数
    function editParameter(paramName, paramAddr) {
        const currentValue = document.getElementById(paramName).textContent;
        document.getElementById('paramName').value = paramName;
        document.getElementById('paramAddr').value = paramAddr;
        document.getElementById('paramValue').value = currentValue !== '--' ? currentValue : '';

        // 设置参数名称和描述
        document.getElementById('paramNameLabel').textContent = paramName;

        // 获取参数描述
        const paramRow = document.querySelector(`tr:has(button[onclick="editParameter('${paramName}', ${paramAddr})"])`);
        const paramDescription = paramRow.querySelector('td:nth-child(4)').textContent;
        document.getElementById('paramDescription').textContent = paramDescription;

        // 更新模态框标题
        document.getElementById('editParameterModalLabel').textContent = `编辑参数 - ${paramName}`;

        const modal = new bootstrap.Modal(document.getElementById('editParameterModal'));
        modal.show();
    }

    // 保存参数
    function saveParameter() {
        const paramName = document.getElementById('paramName').value;
        const paramAddr = document.getElementById('paramAddr').value;
        const paramValue = document.getElementById('paramValue').value;

        if (!paramValue) {
            alert('请输入参数值');
            return;
        }

        // 发送请求保存参数
        fetch('/api/device/{{ device.id }}/parameters', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reg_addr: paramAddr,
                reg_value: paramValue
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新所有相同ID的元素显示
                const elements = document.querySelectorAll(`[id="${paramName}"]`);
                elements.forEach(element => {
                    element.textContent = paramValue;
                });

                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('editParameterModal')).hide();
                alert('参数保存成功');
            } else {
                alert('参数保存失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('保存参数失败:', error);
            alert('保存参数失败，请重试');
        });
    }

    // 查询错误计数
    function queryErrorCounts() {
        // 显示加载中提示
        const errorBtn = document.querySelector('button[onclick="queryErrorCounts()"]');
        const originalText = errorBtn.innerHTML;
        errorBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        errorBtn.disabled = true;

        // 发送请求获取错误计数
        fetch('/api/device/{{ device.id }}/error_counts')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('获取错误计数失败: ' + data.error);
                } else {
                    let message = '错误计数查询结果：\n\n';
                    message += `错误计数1 (REG_ERROR_CNT1): ${data.REG_ERROR_CNT1 || 0}\n`;
                    message += `错误计数2 (REG_ERROR_CNT2): ${data.REG_ERROR_CNT2 || 0}\n`;
                    message += `错误计数3 (REG_ERROR_CNT3): ${data.REG_ERROR_CNT3 || 0}\n`;
                    message += `错误计数4 (REG_ERROR_CNT4): ${data.REG_ERROR_CNT4 || 0}`;
                    alert(message);
                }
            })
            .catch(error => {
                console.error('获取错误计数失败:', error);
                alert('获取错误计数失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                errorBtn.innerHTML = originalText;
                errorBtn.disabled = false;
            });
    }

    // 查询设备固件信息
    function queryFirmwareInfo(isRefresh = false) {
        // 显示模态框
        if (!isRefresh) {
            const modal = new bootstrap.Modal(document.getElementById('firmwareInfoModal'));
            modal.show();
        }

        // 显示加载中状态
        document.getElementById('firmwareInfoLoading').classList.remove('d-none');
        document.getElementById('firmwareInfoContent').classList.add('d-none');
        document.getElementById('firmwareInfoError').classList.add('d-none');

        // 发送请求获取设备固件信息
        fetch('/api/device/{{ device.id }}/firmware_info')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中状态
                document.getElementById('firmwareInfoLoading').classList.add('d-none');

                if (data.error) {
                    // 显示错误信息
                    document.getElementById('firmwareInfoError').textContent = '获取设备固件信息失败: ' + data.error;
                    document.getElementById('firmwareInfoError').classList.remove('d-none');
                    return;
                }

                // 解析并显示固件信息
                displayFirmwareInfo(data);

                // 显示内容区域
                document.getElementById('firmwareInfoContent').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取设备固件信息失败:', error);
                // 隐藏加载中状态，显示错误信息
                document.getElementById('firmwareInfoLoading').classList.add('d-none');
                document.getElementById('firmwareInfoError').textContent = '获取设备固件信息失败: ' + error.message;
                document.getElementById('firmwareInfoError').classList.remove('d-none');
            });
    }

    // 显示固件信息
    function displayFirmwareInfo(data) {
        // 检查是否有固件信息
        if (!data) {
            document.getElementById('firmwareInfoError').textContent = '设备返回的固件信息格式不正确';
            document.getElementById('firmwareInfoError').classList.remove('d-none');
            return;
        }

        // 根据实际返回的数据结构进行处理
        // 数据格式: {'session_id': 1, 'result': 0, 'info': {...}}
        if (data.result !== 0) {
            document.getElementById('firmwareInfoError').textContent = '设备返回错误: ' + data.result;
            document.getElementById('firmwareInfoError').classList.remove('d-none');
            return;
        }

        if (!data.info) {
            document.getElementById('firmwareInfoError').textContent = '设备返回的固件信息不包含info字段';
            document.getElementById('firmwareInfoError').classList.remove('d-none');
            return;
        }

        const info = data.info;

        // 格式化函数
        function formatSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatHex(value) {
            return '0x' + value.toString(16).toUpperCase().padStart(8, '0');
        }

        function formatTimestamp(timestamp) {
            if (timestamp === 0) return '未设置';
            return new Date(timestamp * 1000).toLocaleString();
        }

        function formatVersion(version) {
            // 假设版本格式为 0xAABBCCDD，其中 AA.BB.CC.DD
            const major = (version >> 24) & 0xFF;
            const minor = (version >> 16) & 0xFF;
            const patch = (version >> 8) & 0xFF;
            const build = version & 0xFF;
            return `${major}.${minor}.${patch}.${build}`;
        }

        // 更新FW1信息
        document.getElementById('fw1_size').textContent = formatSize(info.fw1_size || 0);
        document.getElementById('fw1_crc32').textContent = formatHex(info.fw1_crc32 || 0);
        document.getElementById('fw1_update_time').textContent = formatTimestamp(info.fw1_update_time || 0);
        document.getElementById('fw1_capacity').textContent = formatSize(info.fw1_capacity || 0);
        document.getElementById('fw1_version').textContent = formatVersion(info.fw1_version || 0);
        document.getElementById('compile_ts').textContent = formatTimestamp(info.compile_ts || 0);
        
        // 更新FW2信息
        document.getElementById('fw2_size').textContent = formatSize(info.fw2_size || 0);
        document.getElementById('fw2_crc32').textContent = formatHex(info.fw2_crc32 || 0);
        document.getElementById('fw2_update_time').textContent = formatTimestamp(info.fw2_update_time || 0);
        document.getElementById('fw2_capacity').textContent = formatSize(info.fw2_capacity || 0);
        document.getElementById('fw2_version').textContent = formatVersion(info.fw2_version || 0);

        // 更新系统信息
        document.getElementById('bootloader_version').textContent = formatVersion(info.bootloader_version || 0);
        document.getElementById('device_type').textContent = info.device_type_name;

        // 更新UID信息
        if (info.uid && Array.isArray(info.uid) && info.uid.length >= 3) {
            document.getElementById('uid_0').textContent = formatHex(info.uid[0]);
            document.getElementById('uid_1').textContent = formatHex(info.uid[1]);
            document.getElementById('uid_2').textContent = formatHex(info.uid[2]);
        } else {
            document.getElementById('uid_0').textContent = '--';
            document.getElementById('uid_1').textContent = '--';
            document.getElementById('uid_2').textContent = '--';
        }
    }

    // 查询设备调试信息
    function queryDebugInfo(isRefresh = false) {
        // 显示模态框
        if (!isRefresh) {
            const modal = new bootstrap.Modal(document.getElementById('debugInfoModal'));
            modal.show();
        }

        // 显示加载中状态
        document.getElementById('debugInfoLoading').classList.remove('d-none');
        document.getElementById('debugInfoContent').classList.add('d-none');
        document.getElementById('debugInfoError').classList.add('d-none');

        // 发送请求获取设备调试信息
        fetch('/api/device/{{ device.id }}/debug_info')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中状态
                document.getElementById('debugInfoLoading').classList.add('d-none');

                if (data.error) {
                    // 显示错误信息
                    document.getElementById('debugInfoError').textContent = '获取设备调试信息失败: ' + data.error;
                    document.getElementById('debugInfoError').classList.remove('d-none');
                    return;
                }

                // 解析并显示调试信息
                displayDebugInfo(data);

                // 显示内容区域
                document.getElementById('debugInfoContent').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取设备调试信息失败:', error);
                // 隐藏加载中状态，显示错误信息
                document.getElementById('debugInfoLoading').classList.add('d-none');
                document.getElementById('debugInfoError').textContent = '获取设备调试信息失败: ' + error.message;
                document.getElementById('debugInfoError').classList.remove('d-none');
            });
    }

    // 显示调试信息
    function displayDebugInfo(data) {
        // 检查是否有调试信息
        if (!data) {
            document.getElementById('debugInfoError').textContent = '设备返回的调试信息格式不正确';
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        // 根据实际返回的数据结构进行处理
        // 数据格式: {'session_id': 1, 'result': 0, 'info': {...}}
        if (data.result !== 0) {
            document.getElementById('debugInfoError').textContent = '设备返回错误: ' + data.result;
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        if (!data.info) {
            document.getElementById('debugInfoError').textContent = '设备返回的调试信息不包含info字段';
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        const info = data.info;

        // 更新错误计数
        document.getElementById('bl0910_error_count').textContent = info.bl0910_error_count || '0';
        document.getElementById('short_period_error_count').textContent = info.short_period_error_count || '0';
        document.getElementById('long_period_error_count').textContent = info.long_period_error_count || '0';

        // 更新继电器状态
        document.getElementById('relay_state_value').textContent = `继电器状态值: ${info.relay_state || '0'} (0x${(info.relay_state || 0).toString(16).toUpperCase().padStart(4, '0')})`;

        // 更新继电器状态位
        const relayBitsContainer = document.getElementById('relay_bits_container');
        relayBitsContainer.innerHTML = '';

        // 如果有继电器状态位信息，使用它
        if (info.relay_bits) {
            for (let i = 0; i < 16; i++) {
                const bitName = `relay_${i}`;
                const bitValue = info.relay_bits[bitName];
                const bitStatus = bitValue ? '开启' : '关闭';
                const bitClass = bitValue ? 'success' : 'secondary';

                const bitElement = document.createElement('div');
                bitElement.className = 'col-md-3 col-6 mb-2';
                bitElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-${bitClass} me-2">${i}</span>
                        <span>${bitStatus}</span>
                    </div>
                `;
                relayBitsContainer.appendChild(bitElement);
            }
        } else {
            // 如果没有继电器状态位信息，但有继电器状态值，则手动计算
            const relayState = info.relay_state || 0;
            for (let i = 0; i < 16; i++) {
                const bitValue = (relayState & (1 << i)) !== 0;
                const bitStatus = bitValue ? '开启' : '关闭';
                const bitClass = bitValue ? 'success' : 'secondary';

                const bitElement = document.createElement('div');
                bitElement.className = 'col-md-3 col-6 mb-2';
                bitElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-${bitClass} me-2">${i}</span>
                        <span>${bitStatus}</span>
                    </div>
                `;
                relayBitsContainer.appendChild(bitElement);
            }
        }

        // 更新BL0910 RMS寄存器值
        const rmsRegsContainer = document.getElementById('bl0910_rms_regs_container');
        rmsRegsContainer.innerHTML = '';

        if (info.bl0910_rms_regs && Array.isArray(info.bl0910_rms_regs)) {
            info.bl0910_rms_regs.forEach((value, index) => {
                // 计算功率值
                const powerValue = calculatePowerFromRegister(value, index);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>通道 ${index + 1}</td>
                    <td>${value} (0x${value.toString(16).toUpperCase().padStart(8, '0')})</td>
                    <td>${powerValue} W</td>
                `;
                rmsRegsContainer.appendChild(row);
            });
        }

        // 更新零交叉时间
        document.getElementById('zero_cross_time').textContent = info.zero_cross_time ? `${info.zero_cross_time} ms` : '--';

        // 更新新增的传感器数据
        if (info.voltage !== undefined) {
            document.getElementById('voltage_value').textContent = `${(info.voltage / 100).toFixed(2)} V`;
        } else {
            document.getElementById('voltage_value').textContent = '--';
        }

        if (info.temperature !== undefined) {
            document.getElementById('temperature_value').textContent = `${(info.temperature / 100).toFixed(2)} °C`;
        } else {
            document.getElementById('temperature_value').textContent = '--';
        }

        if (info.total_power !== undefined) {
            document.getElementById('total_power_value').textContent = `${(info.total_power / 100).toFixed(2)} W`;
        } else {
            document.getElementById('total_power_value').textContent = '--';
        }

        if (info.csq !== undefined) {
            let csqQuality = '';
            if (info.csq >= 20) csqQuality = '(优)';
            else if (info.csq >= 15) csqQuality = '(良)';
            else if (info.csq >= 10) csqQuality = '(中)';
            else csqQuality = '(差)';

            // 计算dBm值
            let dBm = '';
            if (info.csq === 0) dBm = '≤-115dBm';
            else if (info.csq === 1) dBm = '-111dBm';
            else if (info.csq >= 2 && info.csq <= 30) dBm = `-${113 - (info.csq*2)}dBm`;
            else if (info.csq === 31) dBm = '≥-51dBm';
            else if (info.csq === 99) dBm = '未知';

            document.getElementById('csq_value').textContent = `${info.csq} ${csqQuality} [${dBm}]`;
        } else {
            document.getElementById('csq_value').textContent = '--';
        }

        if (info.ber !== undefined) {
            let berText = '';
            if (info.ber >= 0 && info.ber <= 7) {
                berText = `${info.ber} (RXQUAL值)`;
            } else if (info.ber === 99) {
                berText = `${info.ber} (未知或不可测)`;
            } else {
                berText = `${info.ber}`;
            }
            document.getElementById('ber_value').textContent = berText;
        } else {
            document.getElementById('ber_value').textContent = '--';
        }
    }

    // 打开调试脚本模态框
    function openDebugScriptModal() {
        const modal = new bootstrap.Modal(document.getElementById('debugScriptModal'));
        modal.show();
    }

    function querySimInfo() {
        // 显示加载中提示
        const errorBtn = document.querySelector('button[onclick="querySimInfo()"]');
        const originalText = errorBtn.innerHTML;
        errorBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        errorBtn.disabled = true;

        // 发送请求获取SIM卡信息
        fetch('/api/device/{{ device.id }}/sim_card_info')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('获取错误SIM信息失败: ' + data.error);
                } else {
                    let message = 'SIM信息查询结果：\n\n';
                    message += `IMEI 长度 (IMEI_LEN): ${data.imei_len || '未知'}\n`;
                    message += `IMEI (IMEI): ${data.imei || '未知'}\n`;
                    message += `ICCID 长度 (ICCID_LEN): ${data.iccid_len || '未知'}\n`;
                    message += `ICCID (ICCID): ${data.iccid || '未知'}\n`;
                    message += `查询类型 (QUERY_TYPE): ${data.query_type || '未知'}\n`;
                    alert(message);
                }
            })
            .catch(error => {
                console.error('获取错误SIM信息失败:', error);
                alert('获取SIM信息失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                errorBtn.innerHTML = originalText;
                errorBtn.disabled = false;
            });
    }

    // 初始化设备参数导出功能
    document.addEventListener('DOMContentLoaded', function() {
        const exportBtn = document.getElementById('exportParametersBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                exportDeviceParameters();
            });
        }
    });

    // 导出设备参数
    async function exportDeviceParameters() {
        try {
            const confirmResult = confirm('确定要导出设备参数吗？');
            if (!confirmResult) {
                return;
            }

            const exportBtn = document.getElementById('exportParametersBtn');
            const originalHTML = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
            exportBtn.disabled = true;

            const response = await fetch(`/debug_script/export_device_parameters/{{ device.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    format: 'xlsx'
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '导出失败');
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // 从响应头获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = `{{ device.device_id }}_parameters_${new Date().toISOString().slice(0,10)}.xlsx`;
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                }
            }

            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            alert('设备参数导出成功！');

        } catch (error) {
            console.error('导出设备参数失败:', error);
            alert('导出失败: ' + error.message);
        } finally {
            const exportBtn = document.getElementById('exportParametersBtn');
            exportBtn.innerHTML = '<i class="fas fa-download me-1"></i> 导出参数';
            exportBtn.disabled = false;
        }
    }

</script>
{% endblock %}