{% extends "base.html" %}

{% block title %}批量OTA更新{% endblock %}

{% block styles %}
<style>
    .progress {
        height: 20px;
    }
    .table th {
        border-top: none;
    }
    .badge {
        font-size: 0.85em;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-sync-alt text-primary me-2"></i>批量OTA更新
        </h2>
        <button type="button" class="btn btn-primary" onclick="startBatchOta()">
            <i class="fas fa-rocket me-1"></i>启动全体批量更新
        </button>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
    </div>
    {% endif %}

    <!-- 批量更新历史记录 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-history me-2"></i>批量更新历史记录
            </h5>
        </div>
        <div class="card-body">
            {% if reports %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>批次ID</th>
                            <th>状态</th>
                            <th>设备总数</th>
                            <th>成功</th>
                            <th>失败</th>
                            <th>跳过</th>
                            <th>进度</th>
                            <th>开始时间</th>
                            <th>完成时间</th>
                            <th>创建者</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for report in reports %}
                        <tr>
                            <td>
                                <code>{{ report.batch_id }}</code>
                            </td>
                            <td>
                                {% if report.status == '进行中' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-spinner fa-spin me-1"></i>{{ report.status }}
                                </span>
                                {% elif report.status == '已完成' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>{{ report.status }}
                                </span>
                                {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>{{ report.status }}
                                </span>
                                {% endif %}
                            </td>
                            <td>{{ report.total_devices }}</td>
                            <td>
                                <span class="text-success">
                                    <i class="fas fa-check me-1"></i>{{ report.success_count }}
                                </span>
                            </td>
                            <td>
                                <span class="text-danger">
                                    <i class="fas fa-times me-1"></i>{{ report.failed_count }}
                                </span>
                            </td>
                            <td>
                                <span class="text-info">
                                    <i class="fas fa-forward me-1"></i>{{ report.skipped_count }}
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="width: 100px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ report.progress_percentage }}%"
                                         aria-valuenow="{{ report.progress_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ report.progress_percentage }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <small>{{ report.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </td>
                            <td>
                                {% if report.completed_at %}
                                <small>{{ report.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ report.created_by or '-' }}</td>
                            <td>
                                <a href="{{ url_for('batch_ota.batch_ota_detail', batch_id=report.batch_id) }}"
                                   class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-eye me-1"></i>查看详情
                                </a>
                                {% if report.status != '进行中' %}
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="deleteBatchReport('{{ report.batch_id }}')">
                                    <i class="fas fa-trash me-1"></i>删除
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无批量更新记录</p>
                <p class="text-muted">点击上方按钮启动第一次批量更新</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 启动确认模态框 -->
<div class="modal fade" id="startBatchOtaModal" tabindex="-1" aria-labelledby="startBatchOtaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="startBatchOtaModalLabel">
                    <i class="fas fa-rocket text-primary me-2"></i>启动批量OTA更新
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>此操作将对设备进行固件更新检查和升级。
                </div>

                <!-- 更新选项 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <h6><i class="fas fa-cog me-2"></i>更新选项</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skipVersionCheck">
                            <label class="form-check-label" for="skipVersionCheck">
                                跳过版本检查（强制更新所有设备）
                            </label>
                            <small class="form-text text-muted d-block">勾选后将忽略版本比较，强制更新所有设备</small>
                        </div>
                    </div>
                </div>

                <!-- 设备过滤选项 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <h6><i class="fas fa-filter me-2"></i>设备过滤（排除以下设备）</h6>
                        <div class="mb-2">
                            <label for="excludeDeviceIds" class="form-label">排除设备ID（逗号分隔）</label>
                            <input type="text" class="form-control" id="excludeDeviceIds"
                                   placeholder="例如: 9999,9998,100001001">
                            <small class="form-text text-muted">留空表示不排除任何设备</small>
                        </div>
                        <div class="mb-2">
                            <label for="excludeProductKeys" class="form-label">排除产品密钥（逗号分隔）</label>
                            <input type="text" class="form-control" id="excludeProductKeys"
                                   placeholder="例如: wx78f785cb27a63a65">
                        </div>
                        <div class="mb-2">
                            <label for="excludeKeywords" class="form-label">排除关键字（逗号分隔）</label>
                            <input type="text" class="form-control" id="excludeKeywords"
                                   placeholder="例如: 测试,demo,临时">
                            <small class="form-text text-muted">匹配设备ID或设备备注中的关键字</small>
                        </div>
                    </div>
                </div>

                <p>系统将执行以下操作：</p>
                <ul>
                    <li>查询设备的固件版本信息（超时时间3秒）</li>
                    <li>根据设备类型匹配最新固件</li>
                    <li>对需要更新的设备执行OTA升级</li>
                    <li>顺序处理设备，避免系统负载过高</li>
                </ul>
                <p class="text-muted">此过程可能需要较长时间，请耐心等待。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmStartBatchOta()">
                    <i class="fas fa-rocket me-1"></i>确认启动
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function startBatchOta() {
    // 显示确认模态框
    const modal = new bootstrap.Modal(document.getElementById('startBatchOtaModal'));
    modal.show();
}

function confirmStartBatchOta() {
    // 获取表单数据
    const skipVersionCheck = document.getElementById('skipVersionCheck').checked;
    const excludeDeviceIds = document.getElementById('excludeDeviceIds').value.trim();
    const excludeProductKeys = document.getElementById('excludeProductKeys').value.trim();
    const excludeKeywords = document.getElementById('excludeKeywords').value.trim();

    // 隐藏模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('startBatchOtaModal'));
    modal.hide();

    // 显示加载状态
    showNotification('正在启动批量OTA更新...', 'info');

    // 准备请求数据
    const requestData = {
        skip_version_check: skipVersionCheck,
        exclude_device_ids: excludeDeviceIds,
        exclude_product_keys: excludeProductKeys,
        exclude_keywords: excludeKeywords
    };

    // 发送启动请求
    fetch('/api/batch_ota/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // 3秒后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('启动批量OTA更新失败:', error);
        showNotification('启动失败，请重试', 'error');
    });
}

function deleteBatchReport(batchId) {
    if (!confirm('确定要删除这个批次记录吗？此操作不可恢复。')) {
        return;
    }

    showNotification('正在删除批次记录...', 'info');

    fetch(`/api/batch_ota/${batchId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('批次记录删除成功', 'success');
            // 2秒后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除批次记录失败:', error);
        showNotification('删除失败，请重试', 'error');
    });
}

function showNotification(message, type) {
    // 简单的通知实现
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
