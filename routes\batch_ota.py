from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from models.database import db
from models.batch_ota_report import BatchOtaReport, BatchOtaDetail
from services.batch_ota_service import batch_ota_service
from utils.logger import LoggerManager

logger = LoggerManager.get_logger()

batch_ota_bp = Blueprint('batch_ota', __name__)

@batch_ota_bp.route('/batch_ota')
@login_required
def batch_ota_list():
    """批量OTA报表列表页面"""
    try:
        # 获取所有批量OTA报表，按开始时间倒序
        reports = BatchOtaReport.query.order_by(BatchOtaReport.started_at.desc()).all()
        return render_template('batch_ota/list.html', reports=reports)
    except Exception as e:
        logger.error(f"获取批量OTA报表列表失败: {e}")
        return render_template('batch_ota/list.html', reports=[], error=str(e))

@batch_ota_bp.route('/batch_ota/<batch_id>')
@login_required
def batch_ota_detail(batch_id):
    """批量OTA详细报表页面"""
    try:
        # 获取报表基本信息
        report = BatchOtaReport.query.filter_by(batch_id=batch_id).first_or_404()
        
        # 获取详细记录，按状态和设备ID排序
        details = BatchOtaDetail.query.filter_by(batch_id=batch_id).order_by(
            BatchOtaDetail.status.desc(),
            BatchOtaDetail.device_id
        ).all()
        
        return render_template('batch_ota/detail.html', report=report, details=details)
    except Exception as e:
        logger.error(f"获取批量OTA详细报表失败: {e}")
        return redirect(url_for('batch_ota.batch_ota_list'))

@batch_ota_bp.route('/api/batch_ota/start', methods=['POST'])
@login_required
def start_batch_ota():
    """启动批量OTA更新"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        skip_version_check = data.get('skip_version_check', False)
        exclude_device_ids = data.get('exclude_device_ids', '').strip()
        exclude_product_keys = data.get('exclude_product_keys', '').strip()
        exclude_keywords = data.get('exclude_keywords', '').strip()

        # 启动批量OTA更新
        result = batch_ota_service.start_batch_ota(
            created_by=current_user.username if current_user else None,
            skip_version_check=skip_version_check,
            exclude_device_ids=exclude_device_ids if exclude_device_ids else None,
            exclude_product_keys=exclude_product_keys if exclude_product_keys else None,
            exclude_keywords=exclude_keywords if exclude_keywords else None
        )

        return jsonify(result)
        
    except Exception as e:
        logger.error(f"启动批量OTA更新失败: {e}")
        return jsonify({
            'success': False,
            'message': f'启动失败: {str(e)}',
            'batch_id': None
        })

@batch_ota_bp.route('/api/batch_ota/<batch_id>/status')
@login_required
def get_batch_status(batch_id):
    """获取批次状态"""
    try:
        status = batch_ota_service.get_batch_status(batch_id)
        if status:
            return jsonify({'success': True, 'data': status})
        else:
            return jsonify({'success': False, 'message': '批次不存在'})
    except Exception as e:
        logger.error(f"获取批次状态失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@batch_ota_bp.route('/api/batch_ota/<batch_id>/details')
@login_required
def get_batch_details(batch_id):
    """获取批次详细信息"""
    try:
        # 获取详细记录
        details = BatchOtaDetail.query.filter_by(batch_id=batch_id).all()
        
        result = []
        for detail in details:
            result.append({
                'id': detail.id,
                'device_id': detail.device_id,
                'device_name': detail.device_name,
                'device_remark': detail.device_remark,
                'device_type': detail.device_type,
                'current_firmware_crc': detail.current_firmware_crc,
                'target_firmware_version': detail.target_firmware_version,
                'target_firmware_crc': detail.target_firmware_crc,
                'status': detail.status,
                'stage': detail.stage,
                'error_message': detail.error_message,
                'started_at': detail.started_at.strftime('%Y-%m-%d %H:%M:%S') if detail.started_at else None,
                'completed_at': detail.completed_at.strftime('%Y-%m-%d %H:%M:%S') if detail.completed_at else None,
                'duration_seconds': detail.duration_seconds
            })
        
        return jsonify({'success': True, 'data': result})
        
    except Exception as e:
        logger.error(f"获取批次详细信息失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@batch_ota_bp.route('/api/batch_ota/create_tables', methods=['POST'])
@login_required
def create_tables():
    """创建批量OTA相关表（仅用于初始化）"""
    try:
        # 创建表
        db.create_all()
        
        return jsonify({
            'success': True,
            'message': '表创建成功'
        })

    except Exception as e:
        logger.error(f"创建表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建表失败: {str(e)}'
        })

@batch_ota_bp.route('/api/batch_ota/<batch_id>/delete', methods=['DELETE'])
@login_required
def delete_batch_report(batch_id):
    """删除批量OTA报表"""
    try:
        # 检查批次是否存在
        report = BatchOtaReport.query.filter_by(batch_id=batch_id).first()
        if not report:
            return jsonify({
                'success': False,
                'message': '批次不存在'
            }), 404

        # 允许删除所有状态的批次（包括正在运行的）
        # 如果是正在运行的批次，先尝试停止
        if report.status == '进行中':
            logger.warning(f"强制删除正在运行的批次: {batch_id}")
            # 这里可以添加停止批次的逻辑，但现在直接删除

        # 删除详细记录
        BatchOtaDetail.query.filter_by(batch_id=batch_id).delete()

        # 删除报表记录
        db.session.delete(report)
        db.session.commit()

        logger.info(f"批量OTA报表已删除: {batch_id}")
        return jsonify({
            'success': True,
            'message': '批次删除成功'
        })

    except Exception as e:
        logger.error(f"删除批量OTA报表失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500
