-- 批量OTA功能数据库迁移脚本
-- 创建时间: 2025-09-16
-- 说明: 添加批量OTA报表和详细记录表

-- ========================================
-- 测试环境 (kfchargingdbgc_schema)
-- ========================================

-- 1. 创建批量OTA报表表
CREATE TABLE IF NOT EXISTS kfchargingdbgc_schema.batch_ota_report (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL UNIQUE,
    total_devices INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    failed_count INTEGER NOT NULL DEFAULT 0,
    skipped_count INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT '进行中',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP DEFAULT NULL,
    created_by VARCHAR(80) DEFAULT NULL,
    CONSTRAINT chk_batch_ota_report_status CHECK (status IN ('进行中', '已完成', '已取消'))
);

-- 添加注释
COMMENT ON TABLE kfchargingdbgc_schema.batch_ota_report IS '批量OTA更新报表';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.batch_id IS '批次唯一标识';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.total_devices IS '总设备数量';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.success_count IS '成功更新数量';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.failed_count IS '失败数量';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.skipped_count IS '跳过数量（无需更新）';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.status IS '批次状态';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.started_at IS '开始时间';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.completed_at IS '完成时间';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_report.created_by IS '创建者';

-- 2. 创建批量OTA详细记录表
CREATE TABLE IF NOT EXISTS kfchargingdbgc_schema.batch_ota_detail (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL REFERENCES kfchargingdbgc_schema.batch_ota_report(batch_id) ON DELETE CASCADE,
    device_id INTEGER NOT NULL REFERENCES kfchargingdbgc_schema.device(id) ON DELETE CASCADE,
    device_type INTEGER DEFAULT NULL,
    current_firmware_crc VARCHAR(8) DEFAULT NULL,
    target_firmware_id INTEGER DEFAULT NULL REFERENCES kfchargingdbgc_schema.firmware(id) ON DELETE SET NULL,
    target_firmware_crc VARCHAR(8) DEFAULT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '等待中',
    stage VARCHAR(50) DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    started_at TIMESTAMP DEFAULT NULL,
    completed_at TIMESTAMP DEFAULT NULL,
    CONSTRAINT chk_batch_ota_detail_status CHECK (status IN ('等待中', '校验中', '更新中', '成功', '失败', '跳过')),
    CONSTRAINT chk_batch_ota_detail_device_type CHECK (device_type IS NULL OR device_type IN (10, 50, 51))
);

-- 添加注释
COMMENT ON TABLE kfchargingdbgc_schema.batch_ota_detail IS '批量OTA更新详细记录';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.batch_id IS '关联的批次ID';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.device_id IS '设备ID';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.device_type IS '设备类型';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.current_firmware_crc IS '当前固件CRC32';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.target_firmware_id IS '目标固件ID';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.target_firmware_crc IS '目标固件CRC32';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.status IS '更新状态';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.stage IS '当前阶段';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.error_message IS '错误信息';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.started_at IS '开始时间';
COMMENT ON COLUMN kfchargingdbgc_schema.batch_ota_detail.completed_at IS '完成时间';

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS idx_batch_ota_report_batch_id 
ON kfchargingdbgc_schema.batch_ota_report(batch_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_report_status 
ON kfchargingdbgc_schema.batch_ota_report(status);

CREATE INDEX IF NOT EXISTS idx_batch_ota_report_started_at 
ON kfchargingdbgc_schema.batch_ota_report(started_at);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_batch_id 
ON kfchargingdbgc_schema.batch_ota_detail(batch_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_device_id 
ON kfchargingdbgc_schema.batch_ota_detail(device_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_status 
ON kfchargingdbgc_schema.batch_ota_detail(status);

-- ========================================
-- 生产环境 (kafanglinlin_schema)
-- ========================================

-- 1. 创建批量OTA报表表
CREATE TABLE IF NOT EXISTS kafanglinlin_schema.batch_ota_report (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL UNIQUE,
    total_devices INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    failed_count INTEGER NOT NULL DEFAULT 0,
    skipped_count INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT '进行中',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP DEFAULT NULL,
    created_by VARCHAR(80) DEFAULT NULL,
    CONSTRAINT chk_batch_ota_report_status CHECK (status IN ('进行中', '已完成', '已取消'))
);

-- 添加注释
COMMENT ON TABLE kafanglinlin_schema.batch_ota_report IS '批量OTA更新报表';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.batch_id IS '批次唯一标识';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.total_devices IS '总设备数量';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.success_count IS '成功更新数量';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.failed_count IS '失败数量';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.skipped_count IS '跳过数量（无需更新）';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.status IS '批次状态';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.started_at IS '开始时间';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.completed_at IS '完成时间';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_report.created_by IS '创建者';

-- 2. 创建批量OTA详细记录表
CREATE TABLE IF NOT EXISTS kafanglinlin_schema.batch_ota_detail (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL REFERENCES kafanglinlin_schema.batch_ota_report(batch_id) ON DELETE CASCADE,
    device_id INTEGER NOT NULL REFERENCES kafanglinlin_schema.device(id) ON DELETE CASCADE,
    device_type INTEGER DEFAULT NULL,
    current_firmware_crc VARCHAR(8) DEFAULT NULL,
    target_firmware_id INTEGER DEFAULT NULL REFERENCES kafanglinlin_schema.firmware(id) ON DELETE SET NULL,
    target_firmware_crc VARCHAR(8) DEFAULT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '等待中',
    stage VARCHAR(50) DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    started_at TIMESTAMP DEFAULT NULL,
    completed_at TIMESTAMP DEFAULT NULL,
    CONSTRAINT chk_batch_ota_detail_status CHECK (status IN ('等待中', '校验中', '更新中', '成功', '失败', '跳过')),
    CONSTRAINT chk_batch_ota_detail_device_type CHECK (device_type IS NULL OR device_type IN (10, 50, 51))
);

-- 添加注释
COMMENT ON TABLE kafanglinlin_schema.batch_ota_detail IS '批量OTA更新详细记录';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.batch_id IS '关联的批次ID';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.device_id IS '设备ID';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.device_type IS '设备类型';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.current_firmware_crc IS '当前固件CRC32';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.target_firmware_id IS '目标固件ID';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.target_firmware_crc IS '目标固件CRC32';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.status IS '更新状态';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.stage IS '当前阶段';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.error_message IS '错误信息';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.started_at IS '开始时间';
COMMENT ON COLUMN kafanglinlin_schema.batch_ota_detail.completed_at IS '完成时间';

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS idx_batch_ota_report_batch_id 
ON kafanglinlin_schema.batch_ota_report(batch_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_report_status 
ON kafanglinlin_schema.batch_ota_report(status);

CREATE INDEX IF NOT EXISTS idx_batch_ota_report_started_at 
ON kafanglinlin_schema.batch_ota_report(started_at);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_batch_id 
ON kafanglinlin_schema.batch_ota_detail(batch_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_device_id 
ON kafanglinlin_schema.batch_ota_detail(device_id);

CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_status 
ON kafanglinlin_schema.batch_ota_detail(status);
