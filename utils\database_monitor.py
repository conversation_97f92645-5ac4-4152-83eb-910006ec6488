#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库连接监控工具
提供数据库连接池状态监控和错误处理功能
"""

import time
import threading
from typing import Dict, Any, Optional
from sqlalchemy.exc import SQLAlchemyError, TimeoutError as SQLTimeoutError
from sqlalchemy.pool import QueuePool
from models.database import db
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class DatabaseMonitor:
    """数据库连接监控器"""
    
    def __init__(self):
        self._monitoring = False
        self._monitor_thread = None
        self._stop_event = threading.Event()
        self._stats = {
            'total_connections': 0,
            'active_connections': 0,
            'pool_size': 0,
            'checked_out': 0,
            'overflow': 0,
            'checked_in': 0,
            'last_check_time': 0,
            'connection_errors': 0,
            'timeout_errors': 0
        }
        
    def start_monitoring(self, check_interval: int = 30):
        """开始监控数据库连接状态"""
        if self._monitoring:
            logger.warning("数据库监控已经在运行")
            return
            
        self._monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(check_interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info(f"数据库连接监控已启动，检查间隔: {check_interval}秒")
        
    def stop_monitoring(self):
        """停止监控"""
        if not self._monitoring:
            return
            
        self._monitoring = False
        self._stop_event.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("数据库连接监控已停止")
        
    def _monitor_loop(self, check_interval: int):
        """监控循环"""
        while not self._stop_event.wait(check_interval):
            try:
                self._update_stats()
                self._check_pool_health()
            except Exception as e:
                logger.error(f"数据库监控检查失败: {e}")
                
    def _update_stats(self):
        """更新连接池统计信息"""
        try:
            pool = db.engine.pool
            if isinstance(pool, QueuePool):
                self._stats.update({
                    'pool_size': pool.size(),
                    'checked_out': pool.checkedout(),
                    'overflow': pool.overflow(),
                    'checked_in': pool.checkedin(),
                    'last_check_time': time.time()
                })
                
                # 计算总连接数
                self._stats['total_connections'] = (
                    self._stats['checked_out'] + 
                    self._stats['checked_in'] + 
                    self._stats['overflow']
                )
                self._stats['active_connections'] = self._stats['checked_out']
                
        except Exception as e:
            logger.error(f"更新数据库连接统计失败: {e}")
            
    def _check_pool_health(self):
        """检查连接池健康状态"""
        try:
            stats = self._stats
            
            # 检查连接池使用率
            if stats['pool_size'] > 0:
                usage_rate = stats['active_connections'] / stats['pool_size']
                if usage_rate > 0.8:  # 使用率超过80%
                    logger.warning(
                        f"数据库连接池使用率过高: {usage_rate:.2%} "
                        f"(活跃: {stats['active_connections']}/{stats['pool_size']})"
                    )
                    
            # 检查溢出连接
            if stats['overflow'] > 0:
                logger.warning(f"数据库连接池溢出: {stats['overflow']} 个连接")
                
            # 定期记录连接池状态
            if int(time.time()) % 300 == 0:  # 每5分钟记录一次
                logger.info(
                    f"数据库连接池状态 - "
                    f"总连接: {stats['total_connections']}, "
                    f"活跃: {stats['active_connections']}, "
                    f"池大小: {stats['pool_size']}, "
                    f"溢出: {stats['overflow']}"
                )
                
        except Exception as e:
            logger.error(f"检查连接池健康状态失败: {e}")
            
    def get_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        self._update_stats()
        return self._stats.copy()
        
    def record_error(self, error_type: str):
        """记录错误"""
        if error_type == 'connection':
            self._stats['connection_errors'] += 1
        elif error_type == 'timeout':
            self._stats['timeout_errors'] += 1
            
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with db.engine.connect() as conn:
                conn.execute(db.text('SELECT 1'))
            return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            self.record_error('connection')
            return False


class DatabaseConnectionManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self.monitor = DatabaseMonitor()
        
    def handle_connection_error(self, error: Exception, operation: str = "unknown"):
        """处理数据库连接错误"""
        if isinstance(error, SQLTimeoutError):
            logger.error(f"数据库连接超时 - 操作: {operation}, 错误: {error}")
            self.monitor.record_error('timeout')
            
            # 记录当前连接池状态
            stats = self.monitor.get_stats()
            logger.error(
                f"连接池状态 - 活跃: {stats['active_connections']}, "
                f"池大小: {stats['pool_size']}, 溢出: {stats['overflow']}"
            )
            
        elif isinstance(error, SQLAlchemyError):
            logger.error(f"数据库操作错误 - 操作: {operation}, 错误: {error}")
            self.monitor.record_error('connection')
        else:
            logger.error(f"未知数据库错误 - 操作: {operation}, 错误: {error}")
            
    def safe_execute(self, operation_func, operation_name: str = "database_operation", 
                    default_return=None, max_retries: int = 3):
        """安全执行数据库操作，带重试机制"""
        for attempt in range(max_retries):
            try:
                return operation_func()
            except SQLTimeoutError as e:
                self.handle_connection_error(e, operation_name)
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"数据库操作重试 {attempt + 1}/{max_retries}，等待 {wait_time} 秒")
                    time.sleep(wait_time)
                else:
                    logger.error(f"数据库操作最终失败: {operation_name}")
                    return default_return
            except Exception as e:
                self.handle_connection_error(e, operation_name)
                return default_return
                
        return default_return


# 创建全局实例
db_monitor = DatabaseMonitor()
db_connection_manager = DatabaseConnectionManager()


def init_database_monitoring(app):
    """初始化数据库监控"""
    with app.app_context():
        db_monitor.start_monitoring()
        logger.info("数据库监控已初始化")


def cleanup_database_monitoring():
    """清理数据库监控"""
    db_monitor.stop_monitoring()
    logger.info("数据库监控已清理")
