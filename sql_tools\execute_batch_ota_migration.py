#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量OTA功能数据库迁移执行脚本
创建时间: 2025-09-16
说明: 执行批量OTA相关表的创建和索引建立
"""

import psycopg2
import sys
import os

# 数据库配置
debug_config = {
    'host': '**************',
    'port': 5432,
    'database': 'kfchargingdbg',
    'user': 'KfChargingDbgC',
    'password': 'JT5WJ6Zn3hbAcWBz',
    'schema': 'kfchargingdbgc_schema'
}

def check_table_exists(cursor, schema, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s
        );
    """, (schema, table_name))
    return cursor.fetchone()[0]

def execute_batch_ota_migration():
    """执行批量OTA功能的数据库迁移"""
    try:
        print("开始执行批量OTA功能数据库迁移...")
        print(f"Python版本: {sys.version}")

        # 连接测试数据库
        print(f"连接测试数据库: {debug_config['host']}:{debug_config['port']}/{debug_config['database']}")
        print(f"用户: {debug_config['user']}")

        conn = psycopg2.connect(**{k: v for k, v in debug_config.items() if k != 'schema'})
        conn.autocommit = True
        cursor = conn.cursor()
        print("数据库连接成功")
        
        # 检查表是否已存在
        batch_ota_report_exists = check_table_exists(cursor, debug_config['schema'], 'batch_ota_report')
        batch_ota_detail_exists = check_table_exists(cursor, debug_config['schema'], 'batch_ota_detail')
        
        print(f"batch_ota_report表存在: {batch_ota_report_exists}")
        print(f"batch_ota_detail表存在: {batch_ota_detail_exists}")
        
        # 1. 创建batch_ota_report表
        if not batch_ota_report_exists:
            print("正在创建batch_ota_report表...")
            cursor.execute(f"""
                CREATE TABLE {debug_config['schema']}.batch_ota_report (
                    id SERIAL PRIMARY KEY,
                    batch_id VARCHAR(50) NOT NULL UNIQUE,
                    total_devices INTEGER NOT NULL DEFAULT 0,
                    success_count INTEGER NOT NULL DEFAULT 0,
                    failed_count INTEGER NOT NULL DEFAULT 0,
                    skipped_count INTEGER NOT NULL DEFAULT 0,
                    status VARCHAR(20) NOT NULL DEFAULT '进行中',
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP DEFAULT NULL,
                    created_by VARCHAR(80) DEFAULT NULL,
                    CONSTRAINT chk_batch_ota_report_status CHECK (status IN ('进行中', '已完成', '已取消'))
                );
            """)
            
            # 添加注释
            cursor.execute(f"""
                COMMENT ON TABLE {debug_config['schema']}.batch_ota_report IS '批量OTA更新报表';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.batch_id IS '批次唯一标识';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.total_devices IS '总设备数量';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.success_count IS '成功更新数量';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.failed_count IS '失败数量';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.skipped_count IS '跳过数量（无需更新）';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_report.status IS '批次状态';
            """)
            
            print("batch_ota_report表创建成功")
        else:
            print("batch_ota_report表已存在，跳过创建")
        
        # 2. 创建batch_ota_detail表
        if not batch_ota_detail_exists:
            print("正在创建batch_ota_detail表...")
            cursor.execute(f"""
                CREATE TABLE {debug_config['schema']}.batch_ota_detail (
                    id SERIAL PRIMARY KEY,
                    batch_id VARCHAR(50) NOT NULL REFERENCES {debug_config['schema']}.batch_ota_report(batch_id) ON DELETE CASCADE,
                    device_id INTEGER NOT NULL REFERENCES {debug_config['schema']}.device(id) ON DELETE CASCADE,
                    device_type INTEGER DEFAULT NULL,
                    current_firmware_crc VARCHAR(8) DEFAULT NULL,
                    target_firmware_id INTEGER DEFAULT NULL REFERENCES {debug_config['schema']}.firmware(id) ON DELETE SET NULL,
                    target_firmware_crc VARCHAR(8) DEFAULT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT '等待中',
                    stage VARCHAR(50) DEFAULT NULL,
                    error_message TEXT DEFAULT NULL,
                    started_at TIMESTAMP DEFAULT NULL,
                    completed_at TIMESTAMP DEFAULT NULL,
                    CONSTRAINT chk_batch_ota_detail_status CHECK (status IN ('等待中', '校验中', '更新中', '成功', '失败', '跳过')),
                    CONSTRAINT chk_batch_ota_detail_device_type CHECK (device_type IS NULL OR device_type IN (10, 50, 51))
                );
            """)
            
            # 添加注释
            cursor.execute(f"""
                COMMENT ON TABLE {debug_config['schema']}.batch_ota_detail IS '批量OTA更新详细记录';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.batch_id IS '关联的批次ID';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.device_id IS '设备ID';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.device_type IS '设备类型';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.current_firmware_crc IS '当前固件CRC32';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.target_firmware_id IS '目标固件ID';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.target_firmware_crc IS '目标固件CRC32';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.status IS '更新状态';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.stage IS '当前阶段';
            """)
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.batch_ota_detail.error_message IS '错误信息';
            """)
            
            print("batch_ota_detail表创建成功")
        else:
            print("batch_ota_detail表已存在，跳过创建")
        
        # 3. 创建索引
        print("正在创建索引...")
        
        # batch_ota_report表索引
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_report_batch_id 
            ON {debug_config['schema']}.batch_ota_report(batch_id);
        """)
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_report_status 
            ON {debug_config['schema']}.batch_ota_report(status);
        """)
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_report_started_at 
            ON {debug_config['schema']}.batch_ota_report(started_at);
        """)
        
        # batch_ota_detail表索引
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_batch_id 
            ON {debug_config['schema']}.batch_ota_detail(batch_id);
        """)
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_device_id 
            ON {debug_config['schema']}.batch_ota_detail(device_id);
        """)
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_batch_ota_detail_status 
            ON {debug_config['schema']}.batch_ota_detail(status);
        """)
        
        print("索引创建成功")
        
        # 验证表结构
        print("\n验证表结构...")
        cursor.execute(f"""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = '{debug_config['schema']}' 
            AND table_name = 'batch_ota_report'
            ORDER BY ordinal_position;
        """)
        
        print("batch_ota_report表结构:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1]} {'NULL' if row[2] == 'YES' else 'NOT NULL'} {row[3] or ''}")
        
        cursor.execute(f"""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = '{debug_config['schema']}' 
            AND table_name = 'batch_ota_detail'
            ORDER BY ordinal_position;
        """)
        
        print("\nbatch_ota_detail表结构:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1]} {'NULL' if row[2] == 'YES' else 'NOT NULL'} {row[3] or ''}")
        
        cursor.close()
        conn.close()
        
        print("\n✅ 批量OTA功能数据库迁移完成！")
        print("\n📋 迁移总结:")
        print("- 创建了batch_ota_report表用于存储批量更新报表")
        print("- 创建了batch_ota_detail表用于存储详细更新记录")
        print("- 创建了相关索引以优化查询性能")
        print("- 添加了完整的约束和注释")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

if __name__ == "__main__":
    try:
        print("脚本开始执行...")
        success = execute_batch_ota_migration()
        print(f"脚本执行完成，结果: {success}")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"脚本执行异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
