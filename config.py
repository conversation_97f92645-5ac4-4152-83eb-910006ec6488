import os
from datetime import timedelta
from pathlib import Path

class Config:
    """应用配置类"""
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'

    # PostgreSQL数据库配置
    # 生产环境数据库
    PRODUCTION_DATABASE_URI = (
        'postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/kafangcharging'
        '?options=-csearch_path%3Dkafanglinlin_schema'
    )

    # 调试环境数据库
    DEBUG_DATABASE_URI = (
        '****************************************************************/kfchargingdbg'
        '?options=-csearch_path%3Dkfchargingdbgc_schema'
    )

    # SQLite数据库（向后兼容）
    SQLITE_DATABASE_URI = (
        f'sqlite:///{Path(__file__).parent}/instance/charging_pile.db'
        '?check_same_thread=False&timeout=20'
    )

    # 根据环境变量选择数据库
    print(f"FLASK_ENV:{os.environ.get('FLASK_ENV')}")
    
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or (
        PRODUCTION_DATABASE_URI if os.environ.get('FLASK_ENV') == 'production'
        else DEBUG_DATABASE_URI
    )

    # SQLAlchemy配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0
    }

    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    FIRMWARE_UPLOAD_FOLDER = os.path.join(UPLOAD_FOLDER, 'firmware')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # reCAPTCHA配置
    RECAPTCHA_PUBLIC_KEY = os.environ.get('RECAPTCHA_PUBLIC_KEY') or 'your-public-key'
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY') or 'your-secret-key'

    # InfluxDB Edge 配置（嵌入式模式）
    INFLUXDB_DATA_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'influxdb')
    INFLUXDB_BUCKET = 'charging_pile_data'

    # OTA服务配置
    OTA_MAX_WORKERS = int(os.environ.get('OTA_MAX_WORKERS', 5))  # 最大并发OTA任务数
    OTA_TASK_TIMEOUT = int(os.environ.get('OTA_TASK_TIMEOUT', 300))  # 任务超时时间（秒）
    OTA_MAX_RETRIES = int(os.environ.get('OTA_MAX_RETRIES', 3))  # 最大重试次数

    # 自动OTA升级配置
    AUTO_OTA_ENABLED = os.environ.get('AUTO_OTA_ENABLED', 'true').lower() == 'true'  # 是否启用自动升级
    AUTO_OTA_FORCE_UPDATE = os.environ.get('AUTO_OTA_FORCE_UPDATE', 'true').lower() == 'true'  # 是否强制更新
    AUTO_OTA_TIMEOUT = int(os.environ.get('AUTO_OTA_TIMEOUT', 300))  # 自动升级超时时间（秒）
    AUTO_OTA_MAX_RETRIES = int(os.environ.get('AUTO_OTA_MAX_RETRIES', 3))  # 自动升级最大重试次数

    # 其他配置...
    SESSION_COOKIE_SECURE = False  # True 只通过 HTTPS 传输 cookie
    SESSION_COOKIE_HTTPONLY = True  # 防止 JavaScript 访问 cookie
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # 会话超时时间
    SESSION_COOKIE_SAMESITE = 'Lax'  # 防止 CSRF 攻击
    # WTF_CSRF_ENABLED = False  # 禁用CSRF保护

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = Config.DEBUG_DATABASE_URI

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = Config.PRODUCTION_DATABASE_URI

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = Config.SQLITE_DATABASE_URI  # 测试使用SQLite

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
