#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
手动添加OTA进度跟踪字段到设备表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from models.database import db
from app_factory import create_app

def add_ota_progress_fields():
    """添加OTA进度跟踪字段"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查字段是否已存在
            check_sql = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'device' 
            AND column_name IN ('ota_in_progress', 'ota_start_time');
            """
            
            result = db.session.execute(text(check_sql)).fetchall()
            existing_columns = [row[0] for row in result]
            
            # 添加 ota_in_progress 字段
            if 'ota_in_progress' not in existing_columns:
                add_ota_in_progress_sql = """
                ALTER TABLE device 
                ADD COLUMN ota_in_progress BOOLEAN DEFAULT FALSE;
                """
                db.session.execute(text(add_ota_in_progress_sql))
                print("✅ 已添加 ota_in_progress 字段")
            else:
                print("⚠️ ota_in_progress 字段已存在")
            
            # 添加 ota_start_time 字段
            if 'ota_start_time' not in existing_columns:
                add_ota_start_time_sql = """
                ALTER TABLE device 
                ADD COLUMN ota_start_time TIMESTAMP DEFAULT NULL;
                """
                db.session.execute(text(add_ota_start_time_sql))
                print("✅ 已添加 ota_start_time 字段")
            else:
                print("⚠️ ota_start_time 字段已存在")
            
            # 提交更改
            db.session.commit()
            print("✅ 数据库字段添加完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 添加字段失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("开始添加OTA进度跟踪字段...")
    success = add_ota_progress_fields()
    if success:
        print("🎉 OTA进度跟踪字段添加成功！")
    else:
        print("💥 OTA进度跟踪字段添加失败！")
