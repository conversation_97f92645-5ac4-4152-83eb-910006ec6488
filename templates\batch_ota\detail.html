{% extends "base.html" %}

{% block title %}批量OTA详情 - {{ report.batch_id }}{% endblock %}

{% block styles %}
<style>
    .status-badge {
        font-size: 0.85em;
    }
    .table th {
        border-top: none;
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
    }
    .table-container {
        max-height: 600px;
        overflow-y: auto;
    }
    .progress-mini {
        height: 15px;
        width: 80px;
    }
    .duration-text {
        font-size: 0.8em;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 返回按钮和标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <a href="{{ url_for('batch_ota.batch_ota_list') }}" class="btn btn-outline-secondary me-3">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
            <h2 class="mb-0 d-inline">
                <i class="fas fa-list-alt text-primary me-2"></i>批量OTA详情
            </h2>
        </div>
        {% if not report.is_completed %}
        <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        {% endif %}
    </div>

    <!-- 批次概览 -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>批次概览
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <strong>批次ID:</strong><br>
                            <code>{{ report.batch_id }}</code>
                        </div>
                        <div class="col-md-2">
                            <strong>状态:</strong><br>
                            {% if report.status == '进行中' %}
                            <span class="badge bg-warning status-badge">
                                <i class="fas fa-spinner fa-spin me-1"></i>{{ report.status }}
                            </span>
                            {% elif report.status == '已完成' %}
                            <span class="badge bg-success status-badge">
                                <i class="fas fa-check me-1"></i>{{ report.status }}
                            </span>
                            {% else %}
                            <span class="badge bg-secondary status-badge">
                                <i class="fas fa-times me-1"></i>{{ report.status }}
                            </span>
                            {% endif %}
                        </div>
                        <div class="col-md-2">
                            <strong>总设备数:</strong><br>
                            <span class="h5 text-primary">{{ report.total_devices }}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>成功:</strong><br>
                            <span class="h5 text-success">{{ report.success_count }}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>失败:</strong><br>
                            <span class="h5 text-danger">{{ report.failed_count }}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>跳过:</strong><br>
                            <span class="h5 text-info">{{ report.skipped_count }}</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <strong>开始时间:</strong><br>
                            <small>{{ report.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                        <div class="col-md-3">
                            <strong>完成时间:</strong><br>
                            {% if report.completed_at %}
                            <small>{{ report.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            {% else %}
                            <small class="text-muted">进行中...</small>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <strong>创建者:</strong><br>
                            <small>{{ report.created_by or '系统' }}</small>
                        </div>
                        <div class="col-md-3">
                            <strong>进度:</strong><br>
                            <div class="progress progress-mini">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ report.progress_percentage }}%">
                                    {{ report.progress_percentage }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细记录 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>设备更新详情
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive table-container">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备备注</th>
                            <th>设备类型</th>
                            <th>当前固件CRC</th>
                            <th>目标固件版本</th>
                            <th>目标固件CRC</th>
                            <th>状态</th>
                            <th>当前阶段</th>
                            <th>耗时</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for detail in details %}
                        <tr>
                            <td>
                                <strong>{{ detail.device_name }}</strong>
                            </td>
                            <td>
                                <small>{{ detail.device_remark or '-' }}</small>
                            </td>
                            <td>
                                {% if detail.device_type %}
                                <span class="badge bg-info">
                                    {% if detail.device_type == 10 %}V2{% elif detail.device_type == 50 %}V5{% elif detail.device_type == 51 %}V51{% else %}{{ detail.device_type }}{% endif %}
                                </span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if detail.current_firmware_crc %}
                                <code class="small">{{ detail.current_firmware_crc }}</code>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if detail.target_firmware_version %}
                                <span class="badge bg-primary">v{{ detail.target_firmware_version }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if detail.target_firmware_crc %}
                                <code class="small">{{ detail.target_firmware_crc }}</code>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if detail.status == '等待中' %}
                                <span class="badge bg-secondary status-badge">
                                    <i class="fas fa-clock me-1"></i>{{ detail.status }}
                                </span>
                                {% elif detail.status == '校验中' %}
                                <span class="badge bg-info status-badge">
                                    <i class="fas fa-search me-1"></i>{{ detail.status }}
                                </span>
                                {% elif detail.status == '更新中' %}
                                <span class="badge bg-warning status-badge">
                                    <i class="fas fa-spinner fa-spin me-1"></i>{{ detail.status }}
                                </span>
                                {% elif detail.status == '成功' %}
                                <span class="badge bg-success status-badge">
                                    <i class="fas fa-check me-1"></i>{{ detail.status }}
                                </span>
                                {% elif detail.status == '失败' %}
                                <span class="badge bg-danger status-badge">
                                    <i class="fas fa-times me-1"></i>{{ detail.status }}
                                </span>
                                {% elif detail.status == '跳过' %}
                                <span class="badge bg-info status-badge">
                                    <i class="fas fa-forward me-1"></i>{{ detail.status }}
                                </span>
                                {% else %}
                                <span class="badge bg-secondary status-badge">{{ detail.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ detail.stage or '-' }}</small>
                            </td>
                            <td>
                                {% if detail.duration_seconds > 0 %}
                                <span class="duration-text">{{ "%.1f"|format(detail.duration_seconds) }}s</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if detail.error_message %}
                                <small class="text-danger" title="{{ detail.error_message }}">
                                    {{ detail.error_message[:50] }}{% if detail.error_message|length > 50 %}...{% endif %}
                                </small>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    window.location.reload();
}

// 如果批次还在进行中，每30秒自动刷新
{% if not report.is_completed %}
setInterval(function() {
    window.location.reload();
}, 30000);
{% endif %}
</script>
{% endblock %}
