from datetime import datetime
from models.database import db

class BatchOtaReport(db.Model):
    """批量OTA更新报表模型"""
    __tablename__ = 'batch_ota_report'
    
    id = db.Column(db.Integer, primary_key=True)
    batch_id = db.Column(db.String(50), nullable=False, unique=True)  # 批次ID
    total_devices = db.Column(db.Integer, nullable=False, default=0)  # 总设备数
    success_count = db.Column(db.Integer, nullable=False, default=0)  # 成功数量
    failed_count = db.Column(db.Integer, nullable=False, default=0)  # 失败数量
    skipped_count = db.Column(db.Integer, nullable=False, default=0)  # 跳过数量（无需更新）
    status = db.Column(db.String(20), nullable=False, default="进行中")  # 进行中, 已完成, 已取消
    started_at = db.Column(db.DateTime, default=datetime.now)
    completed_at = db.Column(db.DateTime, nullable=True)
    created_by = db.Column(db.String(80), nullable=True)  # 创建者
    
    # 关联的详细记录
    details = db.relationship('BatchOtaDetail', backref='report', lazy=True, cascade='all, delete-orphan')
    
    @property
    def progress_percentage(self):
        """计算进度百分比"""
        if self.total_devices == 0:
            return 0
        completed = self.success_count + self.failed_count + self.skipped_count
        return round((completed / self.total_devices) * 100, 2)
    
    @property
    def is_completed(self):
        """是否已完成"""
        return self.status in ["已完成", "已取消"]
    
    def update_counts(self):
        """更新统计数据"""
        self.success_count = BatchOtaDetail.query.filter_by(
            batch_id=self.batch_id, status="成功"
        ).count()
        self.failed_count = BatchOtaDetail.query.filter_by(
            batch_id=self.batch_id, status="失败"
        ).count()
        self.skipped_count = BatchOtaDetail.query.filter_by(
            batch_id=self.batch_id, status="跳过"
        ).count()
        
        # 检查是否完成
        completed = self.success_count + self.failed_count + self.skipped_count
        if completed >= self.total_devices and self.status == "进行中":
            self.status = "已完成"
            self.completed_at = datetime.now()
        
        db.session.commit()

class BatchOtaDetail(db.Model):
    """批量OTA更新详细记录模型"""
    __tablename__ = 'batch_ota_detail'
    
    id = db.Column(db.Integer, primary_key=True)
    batch_id = db.Column(db.String(50), db.ForeignKey('batch_ota_report.batch_id'), nullable=False)
    device_id = db.Column(db.Integer, db.ForeignKey('device.id'), nullable=False)
    device_type = db.Column(db.Integer, nullable=True)  # 设备类型
    current_firmware_crc = db.Column(db.String(8), nullable=True)  # 当前固件CRC
    target_firmware_id = db.Column(db.Integer, db.ForeignKey('firmware.id'), nullable=True)  # 目标固件ID
    target_firmware_crc = db.Column(db.String(8), nullable=True)  # 目标固件CRC
    status = db.Column(db.String(20), nullable=False, default="等待中")  # 等待中, 校验中, 更新中, 成功, 失败, 跳过
    stage = db.Column(db.String(50), nullable=True)  # 当前阶段
    error_message = db.Column(db.Text, nullable=True)  # 错误信息
    started_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    # 关联关系
    device = db.relationship('Device', backref='batch_ota_details')
    target_firmware = db.relationship('Firmware', backref='batch_ota_targets')
    
    @property
    def device_name(self):
        """获取设备名称"""
        return self.device.device_id if self.device else "未知设备"
    
    @property
    def device_remark(self):
        """获取设备备注"""
        return self.device.device_remark if self.device else ""
    
    @property
    def target_firmware_version(self):
        """获取目标固件版本"""
        return self.target_firmware.version if self.target_firmware else "未知"
    
    @property
    def duration_seconds(self):
        """获取执行时长（秒）"""
        if not self.started_at:
            return 0
        end_time = self.completed_at or datetime.now()
        return (end_time - self.started_at).total_seconds()
