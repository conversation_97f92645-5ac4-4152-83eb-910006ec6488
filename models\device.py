from datetime import datetime
from models.database import db

class Device(db.Model):
    """设备模型"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(80), unique=True, nullable=False)  # 设备唯一标识符
    device_remark = db.Column(db.String(200))  # 设备备注
    product_key = db.Column(db.String(80), nullable=False)
    firmware_version = db.Column(db.String(20), default="未知")
    device_type = db.Column(db.Integer, nullable=True, default=None)  # 设备类型：10=V2, 50=V5, 51=V51
    last_ota_time = db.Column(db.DateTime, default=None)
    last_ota_status = db.Column(db.String(20), default="未升级")

    ota_start_time = db.Column(db.DateTime, default=None)  # OTA开始时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    @staticmethod
    def get_device_type_name(device_type_value):
        """将设备类型数值转换为对应的版本名称"""
        device_type_map = {
            10: "V2 (旧版霍尔传感器版本，黑色PCB)",
            50: "V5 (新版BL0910 10通道版本)",
            51: "V51 (新版BL0939 2通道版本)"
        }
        return device_type_map.get(device_type_value, f"未知类型 ({device_type_value})") if device_type_value else "未设置"

    @property
    def device_type_name(self):
        """获取设备类型名称"""
        return self.get_device_type_name(self.device_type)