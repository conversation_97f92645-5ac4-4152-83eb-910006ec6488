#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MQTT消息转发服务
实现阿里云和EMQX之间的双向消息转发，支持配置热重载和专用日志记录
"""

import os
import json
import time
import threading
import logging
from datetime import datetime
from typing import Dict, List, Optional, Union, Callable
from pathlib import Path

from iot_client.platform.platform_type import PlatformType
from utils.logger import LoggerManager


class MQTTMessageForwarder:
    """MQTT消息转发服务类"""
    
    def __init__(self, config_path: str = "config/mqtt_forwarder_config.json"):
        """
        初始化MQTT消息转发服务
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = {}
        self.target_devices = set()
        self.forwarding_rules = []
        self.platform_mapping = {}
        
        # 线程控制
        self._stop_event = threading.Event()
        self._config_monitor_thread = None
        self._last_config_mtime = 0
        
        # IoT客户端实例（由外部设置）
        self._iot_client = None
        
        # 日志记录器
        self.logger = LoggerManager.get_logger()
        self._forwarder_logger = None
        
        # 消息回调函数
        self._original_callback = None
        
        # 加载配置
        self._load_config()
        self._setup_forwarder_logger()
        
    def set_iot_client(self, iot_client):
        """设置IoT客户端实例"""
        self._iot_client = iot_client
        
    def start(self):
        """启动转发服务"""
        if not self._iot_client:
            self.logger.error("IoT客户端未设置，无法启动转发服务")
            return False
            
        if not self.config.get('forwarder_config', {}).get('enabled', False):
            self.logger.info("MQTT转发服务已禁用")
            return False
            
        try:
            # 设置消息回调
            self._setup_message_callback()
            
            # 启动配置监控线程
            self._start_config_monitor()
            
            self._forwarder_logger.info("MQTT消息转发服务已启动")
            self.logger.info("MQTT消息转发服务已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动MQTT转发服务失败: {e}")
            return False
            
    def stop(self):
        """停止转发服务"""
        try:
            # 停止配置监控线程
            self._stop_config_monitor()
            
            # 恢复原始消息回调
            if self._original_callback and self._iot_client:
                self._iot_client.on_message_callback = self._original_callback
                
            self._forwarder_logger.info("MQTT消息转发服务已停止")
            self.logger.info("MQTT消息转发服务已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止MQTT转发服务失败: {e}")
            return False
            
    def _load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
                
            # 更新配置
            self.target_devices = set(self.config.get('target_devices', []))
            self.forwarding_rules = self.config.get('forwarding_rules', [])
            self.platform_mapping = self.config.get('platform_mapping', {})
            
            # 更新文件修改时间
            self._last_config_mtime = os.path.getmtime(self.config_path)
            
            self.logger.info(f"已加载转发配置，目标设备: {len(self.target_devices)}台，转发规则: {len(self.forwarding_rules)}条")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
            
    def _setup_forwarder_logger(self):
        """设置转发服务专用日志记录器"""
        try:
            log_config = self.config.get('forwarder_config', {})
            log_file = log_config.get('log_file', 'logs/mqtt_forwarder.log')
            log_level = log_config.get('log_level', 'INFO')
            
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            os.makedirs(log_dir, exist_ok=True)
            
            # 创建专用日志记录器
            self._forwarder_logger = logging.getLogger('mqtt_forwarder')
            self._forwarder_logger.setLevel(getattr(logging, log_level))
            
            # 清除现有处理器
            self._forwarder_logger.handlers.clear()
            
            # 创建文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, log_level))
            
            # 设置日志格式（JSON Lines格式，便于解析）
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "message": %(message)s}'
            )
            file_handler.setFormatter(formatter)
            
            self._forwarder_logger.addHandler(file_handler)
            
            # 防止日志传播到根日志记录器
            self._forwarder_logger.propagate = False
            
        except Exception as e:
            self.logger.error(f"设置转发日志记录器失败: {e}")
            
    def _setup_message_callback(self):
        """设置消息回调函数"""
        if not self._iot_client:
            return
            
        # 保存原始回调
        self._original_callback = getattr(self._iot_client, 'on_message_callback', None)
        
        # 设置新的回调函数
        self._iot_client.on_message_callback = self._on_message_received
        
    def _on_message_received(self, topic: str, payload: Union[bytes, bytearray, str], 
                           qos: int, client_id: str, platform_type: PlatformType):
        """
        消息接收回调函数
        
        Args:
            topic: 主题
            payload: 消息内容
            qos: QoS等级
            client_id: 客户端ID
            platform_type: 平台类型
        """
        try:
            # 调用原始回调（如果存在）
            if self._original_callback:
                self._original_callback(topic, payload, qos, client_id, platform_type)
                
            # 处理转发逻辑
            # self._process_forwarding(topic, payload, platform_type)
            
        except Exception as e:
            self.logger.error(f"处理消息回调时发生错误: {e}")
            
    def _process_forwarding(self, topic: str, payload: Union[bytes, bytearray, str], 
                          source_platform: PlatformType):
        """
        处理消息转发逻辑
        
        Args:
            topic: 源主题
            payload: 消息内容
            source_platform: 源平台类型
        """
        try:
            # 解析主题获取设备ID
            device_id = self._extract_device_id(topic)
            if not device_id or device_id not in self.target_devices:
                return
                
            # 查找匹配的转发规则
            for rule in self.forwarding_rules:
                if not rule.get('enabled', True):
                    continue
                    
                if self._match_forwarding_rule(topic, source_platform, rule):
                    self._forward_message(topic, payload, device_id, rule, source_platform)
                    
        except Exception as e:
            self.logger.error(f"处理消息转发时发生错误: {e}")
            
    def _extract_device_id(self, topic: str) -> Optional[str]:
        """从主题中提取设备ID"""
        try:
            parts = topic.split('/')
            if len(parts) >= 3:
                return parts[2]  # 假设格式为 /product_key/device_id/...
        except Exception:
            pass
        return None
        
    def _match_forwarding_rule(self, topic: str, source_platform: PlatformType, rule: Dict) -> bool:
        """检查主题是否匹配转发规则"""
        try:
            # 检查源平台
            rule_source_platform = rule.get('source_platform', '')
            source_platform_name = self._get_platform_name(source_platform)
            
            if rule_source_platform != source_platform_name:
                return False
                
            # 检查主题模式匹配
            topic_pattern = rule.get('topic_pattern', '')
            return self._match_topic_pattern(topic, topic_pattern)
            
        except Exception:
            return False
            
    def _get_platform_name(self, platform_type: PlatformType) -> str:
        """获取平台名称"""
        if platform_type == PlatformType.ALIBABA_CLOUD:
            return 'alicloud'
        elif platform_type == PlatformType.EMQX:
            return 'emqx'
        return ''
        
    def _match_topic_pattern(self, topic: str, pattern: str) -> bool:
        """检查主题是否匹配模式"""
        try:
            # 简单的模式匹配，支持 {device_id} 占位符
            pattern_parts = pattern.split('/')
            topic_parts = topic.split('/')
            
            if len(pattern_parts) != len(topic_parts):
                return False
                
            for i, (pattern_part, topic_part) in enumerate(zip(pattern_parts, topic_parts)):
                if pattern_part == '{device_id}':
                    continue  # 占位符匹配任意值
                elif pattern_part != topic_part:
                    return False
                    
            return True
            
        except Exception:
            return False
            
    def _forward_message(self, source_topic: str, payload: Union[bytes, bytearray, str], 
                        device_id: str, rule: Dict, source_platform: PlatformType):
        """转发消息"""
        try:
            # 构建目标主题
            target_topic_pattern = rule.get('target_topic_pattern', '')
            target_topic = target_topic_pattern.replace('{device_id}', device_id)
            
            # 获取目标平台类型
            target_platform_name = rule.get('target_platform', '')
            target_platform = self._get_platform_type(target_platform_name)
            
            if target_platform is None:
                self.logger.error(f"无效的目标平台: {target_platform_name}")
                return
                
            # 发送消息
            success = self._iot_client.write_message(target_topic, payload, target_platform)
            
            # 记录转发日志
            self._log_forwarding(device_id, source_topic, target_topic, payload, 
                               source_platform, target_platform, success)
                               
        except Exception as e:
            self.logger.error(f"转发消息时发生错误: {e}")
            
    def _get_platform_type(self, platform_name: str) -> Optional[PlatformType]:
        """根据平台名称获取平台类型"""
        if platform_name == 'alicloud':
            return PlatformType.ALIBABA_CLOUD
        elif platform_name == 'emqx':
            return PlatformType.EMQX
        return None
        
    def _log_forwarding(self, device_id: str, source_topic: str, target_topic: str,
                       payload: Union[bytes, bytearray, str], source_platform: PlatformType,
                       target_platform: PlatformType, success: bool):
        """记录转发日志"""
        try:
            # 转换payload为16进制格式
            if isinstance(payload, str):
                payload_hex = payload.encode('utf-8').hex()
            else:
                payload_hex = payload.hex()
                
            # 构建日志数据
            log_data = {
                "device_id": device_id,
                "source_topic": source_topic,
                "target_topic": target_topic,
                "source_platform": self._get_platform_name(source_platform),
                "target_platform": self._get_platform_name(target_platform),
                "payload_hex": payload_hex,
                "payload_length": len(payload),
                "success": success,
                "direction": f"{self._get_platform_name(source_platform)}→{self._get_platform_name(target_platform)}"
            }
            
            # 记录日志
            if self._forwarder_logger:
                self._forwarder_logger.info(json.dumps(log_data, ensure_ascii=False))
                
        except Exception as e:
            self.logger.error(f"记录转发日志时发生错误: {e}")
            
    def _start_config_monitor(self):
        """启动配置文件监控线程"""
        if self._config_monitor_thread is None or not self._config_monitor_thread.is_alive():
            self._stop_event.clear()
            self._config_monitor_thread = threading.Thread(
                target=self._config_monitor_worker,
                name="MQTTForwarder-ConfigMonitor",
                daemon=True
            )
            self._config_monitor_thread.start()
            
    def _stop_config_monitor(self):
        """停止配置文件监控线程"""
        if self._config_monitor_thread and self._config_monitor_thread.is_alive():
            self._stop_event.set()
            self._config_monitor_thread.join(timeout=5)
            
    def _config_monitor_worker(self):
        """配置文件监控工作线程"""
        check_interval = self.config.get('forwarder_config', {}).get('config_check_interval', 5)
        
        while not self._stop_event.is_set():
            try:
                if os.path.exists(self.config_path):
                    current_mtime = os.path.getmtime(self.config_path)
                    if current_mtime > self._last_config_mtime:
                        self.logger.info("检测到配置文件变更，重新加载配置")
                        if self._load_config():
                            self._setup_forwarder_logger()
                            self.logger.info("配置文件热重载成功")
                        else:
                            self.logger.error("配置文件热重载失败")
                            
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"配置监控线程发生错误: {e}")
                time.sleep(check_interval)


# 全局转发服务实例
mqtt_forwarder = MQTTMessageForwarder()
