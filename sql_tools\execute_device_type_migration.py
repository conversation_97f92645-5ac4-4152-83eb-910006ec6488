#!/usr/bin/env python3
"""
设备类型字段迁移执行脚本
为firmware表和device表添加设备类型字段，并创建latest_firmware表
"""

import os
import sys
import psycopg2
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def execute_migration():
    """执行数据库迁移"""
    
    # 测试环境数据库配置
    debug_config = {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz',
        'schema': 'kfchargingdbgc_schema'
    }
    
    print("=" * 60)
    print("设备类型字段数据库迁移脚本")
    print("=" * 60)
    print(f"开始时间: {datetime.now()}")
    print()
    
    try:
        # 连接测试环境数据库
        print("正在连接测试环境数据库...")
        conn = psycopg2.connect(
            host=debug_config['host'],
            port=debug_config['port'],
            database=debug_config['database'],
            user=debug_config['user'],
            password=debug_config['password']
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 设置搜索路径
        cursor.execute(f"SET search_path TO {debug_config['schema']}, public;")
        print(f"✓ 已连接到测试环境数据库，Schema: {debug_config['schema']}")
        
        # 检查当前表结构
        print("\n检查当前表结构...")
        
        # 检查firmware表是否已有device_type字段
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'firmware' AND column_name = 'device_type'
        """, (debug_config['schema'],))
        firmware_has_device_type = cursor.fetchone() is not None
        
        # 检查device表是否已有device_type字段
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'device' AND column_name = 'device_type'
        """, (debug_config['schema'],))
        device_has_device_type = cursor.fetchone() is not None
        
        # 检查latest_firmware表是否存在
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'latest_firmware'
        """, (debug_config['schema'],))
        latest_firmware_exists = cursor.fetchone() is not None
        
        print(f"firmware表device_type字段: {'已存在' if firmware_has_device_type else '不存在'}")
        print(f"device表device_type字段: {'已存在' if device_has_device_type else '不存在'}")
        print(f"latest_firmware表: {'已存在' if latest_firmware_exists else '不存在'}")
        
        # 执行迁移
        print("\n开始执行迁移...")
        
        # 1. 为firmware表添加设备类型字段
        if not firmware_has_device_type:
            print("正在为firmware表添加device_type字段...")
            cursor.execute(f"""
                ALTER TABLE {debug_config['schema']}.firmware 
                ADD COLUMN device_type INTEGER NOT NULL DEFAULT 50;
            """)
            
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.firmware.device_type 
                IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';
            """)
            
            cursor.execute(f"""
                ALTER TABLE {debug_config['schema']}.firmware 
                ADD CONSTRAINT chk_firmware_device_type 
                CHECK (device_type IN (10, 50, 51));
            """)
            print("✓ firmware表device_type字段添加完成")
        else:
            print("✓ firmware表device_type字段已存在，跳过")
        
        # 2. 为device表添加设备类型字段
        if not device_has_device_type:
            print("正在为device表添加device_type字段...")
            cursor.execute(f"""
                ALTER TABLE {debug_config['schema']}.device 
                ADD COLUMN device_type INTEGER DEFAULT NULL;
            """)
            
            cursor.execute(f"""
                COMMENT ON COLUMN {debug_config['schema']}.device.device_type 
                IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';
            """)
            
            cursor.execute(f"""
                ALTER TABLE {debug_config['schema']}.device 
                ADD CONSTRAINT chk_device_device_type 
                CHECK (device_type IS NULL OR device_type IN (10, 50, 51));
            """)
            print("✓ device表device_type字段添加完成")
        else:
            print("✓ device表device_type字段已存在，跳过")
        
        # 3. 创建latest_firmware表
        if not latest_firmware_exists:
            print("正在创建latest_firmware表...")
            cursor.execute(f"""
                CREATE TABLE {debug_config['schema']}.latest_firmware (
                    id SERIAL PRIMARY KEY,
                    device_type INTEGER NOT NULL UNIQUE,
                    firmware_id INTEGER NOT NULL REFERENCES {debug_config['schema']}.firmware(id) ON DELETE CASCADE,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_by VARCHAR(80) DEFAULT NULL,
                    CONSTRAINT chk_latest_firmware_device_type CHECK (device_type IN (10, 50, 51))
                );
            """)
            
            cursor.execute(f"""
                COMMENT ON TABLE {debug_config['schema']}.latest_firmware IS '各设备类型最新固件管理表';
            """)
            
            cursor.execute(f"""
                CREATE INDEX idx_latest_firmware_device_type 
                ON {debug_config['schema']}.latest_firmware(device_type);
            """)
            
            cursor.execute(f"""
                CREATE INDEX idx_latest_firmware_firmware_id 
                ON {debug_config['schema']}.latest_firmware(firmware_id);
            """)
            print("✓ latest_firmware表创建完成")
        else:
            print("✓ latest_firmware表已存在，跳过")
        
        # 验证迁移结果
        print("\n验证迁移结果...")
        
        # 检查firmware表结构
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'firmware' 
            ORDER BY ordinal_position;
        """, (debug_config['schema'],))
        firmware_columns = cursor.fetchall()
        print(f"firmware表字段数: {len(firmware_columns)}")
        
        # 检查device表结构
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'device' 
            ORDER BY ordinal_position;
        """, (debug_config['schema'],))
        device_columns = cursor.fetchall()
        print(f"device表字段数: {len(device_columns)}")
        
        # 检查latest_firmware表结构
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'latest_firmware' 
            ORDER BY ordinal_position;
        """, (debug_config['schema'],))
        latest_firmware_columns = cursor.fetchall()
        print(f"latest_firmware表字段数: {len(latest_firmware_columns)}")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 60)
        print("✅ 测试环境数据库迁移完成！")
        print("=" * 60)
        print("注意事项：")
        print("1. 请手动执行生产环境迁移脚本")
        print("2. 生产环境迁移脚本已包含在 device_type_migration.sql 文件中")
        print("3. 执行前请确保已备份生产环境数据库")
        print(f"完成时间: {datetime.now()}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = execute_migration()
    sys.exit(0 if success else 1)
